import Google from "next-auth/providers/google";
import LinkedIn from "next-auth/providers/linkedin";
import Credentials from "next-auth/providers/credentials";
import { type NextAuthConfig, type DefaultSession, type User } from "next-auth";

import { tryCatch } from "@/utils/try-catch";
import { getLoginCredentials } from "@/modules/auth/services/login.service";

declare module "next-auth" {
  interface Session {
    user: {
      user: string;
      username: string;
      full_name: string;
      phone_number: string;
      email: string;
      date_of_birth: string;
      gender: string;
      bio: string;
      service_api_key: string;
      cv_file: string | null;
      phone_number_verified: boolean;
      email_verified: boolean;
      is_onboarded: boolean;
      is_staff: boolean;
      isGoogleAuth: boolean;
    } & DefaultSession["user"];
    sessionToken?: string;
  }
  interface User {
    user: string;
    username: string;
    full_name: string;
    phone_number: string;
    email: string;
    date_of_birth: string;
    gender: string;
    bio: string;
    service_api_key: string;
    cv_file: string | null;
    phone_number_verified: boolean;
    email_verified: boolean;
    is_onboarded: boolean;
    is_staff: boolean;
    isGoogleAuth: boolean;
  }
}

export default {
  providers: [
    Google,
    LinkedIn,
    Credentials({
      credentials: {
        username: {},
        password: {},
        remember: {},
      },
      async authorize(credentials) {
        let user = null;

        const [res] = await tryCatch(
          getLoginCredentials({
            username: (credentials?.username as string) ?? "",
            password: (credentials?.password as string) ?? "",
            remember: (credentials?.remember as boolean) ?? false,
          }),
        );

        user = res;

        if (!user) {
          throw new Error("No User Found");
        }

        const trimUser: User = {
          id: user.id.toString(),
          email: user.email,
          name: user.full_name,
          image: user.profile_picture,
          username: user.username,
          date_of_birth: user.date_of_birth,
          gender: user.gender,
          phone_number: user.phone_number,
          full_name: user.full_name,
          user: user.user.toString(),
          bio: user.bio,
          cv_file: user.cv_file,
          service_api_key: user.service_api_key,
          is_staff: user.is_staff,
          phone_number_verified: user.phone_number_verified,
          email_verified: user.email_verified,
          is_onboarded: user.is_onboarded,
          isGoogleAuth: false,
        };

        return trimUser;
      },
    }),
  ],
} satisfies NextAuthConfig;
