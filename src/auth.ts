import NextAuth from "next-auth";
import authConfig from "../auth.config";
import { googleSession } from "./modules/auth/services/login.service";
import { headers } from "next/headers";
import { IdentifyUserMixpanel, TrackEventMixpanel } from "./lib/mixpanel";
import { rewards } from "./modules/gamification/services/rewards.service";

export const {
  handlers,
  signIn,
  signOut,
  auth,
  unstable_update: update,
} = NextAuth({
  trustHost: true,
  secret: process.env.AUTH_SECRET,
  pages: { signIn: "/login" },
  session: { strategy: "jwt" },
  callbacks: {
    async signIn({}) {
      await rewards("login");
      return true;
    },
    async jwt({ token, user, account, trigger, session }) {
    
      if (user) {
        
        token.id = user.id;
        token.email = user.email;
        token.username = user.username;
        token.phone_number = user.phone_number;
        token.date_of_birth = user.date_of_birth;
        token.image = user.image;
        token.gender = user.gender;
        token.user = user.user;
        token.bio = user.bio;
        token.cv_file = user.cv_file;
        token.service_api_key = user.service_api_key;
        token.is_staff = user.is_staff;
        token.phone_number_verified = user.phone_number_verified;
        token.email_verified = user.email_verified;
        token.isGoogleAuth = false;

        if (account && account.provider === "google") {
        const res = await googleSession(account.access_token || "");
        token.id = res?.id ? res?.id : user?.id
        await IdentifyUserMixpanel(token.email,{
          email: token.email,
          oauth: "Google"
        })
        }  

        await TrackEventMixpanel("user_logged_in", {
          distinct_id : token.email,
          user_id : token.id,
          user_email : token.email,
          login_time : new Date().toLocaleString("en-US", { timeZone: "Asia/Jakarta" }),
          platform : (await headers()).get("user-agent"),
          plan_type : "no available property yet"
        })

        token.is_onboarded = user.is_onboarded;
      }

      if (account && account.provider === "linkedin") {
        return { ...token };
      }

      if (account && account.provider === "google") {
        const res = await googleSession(account.access_token || "");

        token.accessToken = account.access_token;
        token.id = res?.id ? res.id : user?.id;
        token.email = user?.email;
        token.username = user?.username;
        token.name = res?.full_name ? res.full_name : user?.name;
        token.phone_number = res?.phone_number ? res.phone_number : user?.phone_number;
        token.date_of_birth = res?.date_of_birth ? res.date_of_birth : user?.date_of_birth;
        token.gender = res?.gender ? res.gender : user?.gender;
        token.user = res?.user ? res.user : user?.user;
        token.bio = res?.bio ? res.bio : user?.bio;
        token.image = res?.profile_picture ? res.profile_picture : user?.image;
        token.profile_picture = res?.profile_picture;
        token.cv_file = res?.cv_file ? res.cv_file : user?.cv_file;
        token.service_api_key = res?.service_api_key ? res.service_api_key : user?.service_api_key;
        token.isGoogleAuth = true;
        token.is_staff = res?.is_staff ? res.is_staff : user?.is_staff;
        token.is_onboarded = res?.is_onboarded ? res.is_onboarded : user?.is_onboarded;
        token.phone_number_verified = res?.phone_number_verified
          ? res.phone_number_verified
          : user?.phone_number_verified;
        token.email_verified = res?.email_verified ? res.email_verified : user?.email_verified;

      
        return { ...token, accessToken: token.accessToken };
      }  

      if (trigger === "update") {
        token.name = session.name;
        token.phone_number = session.phone_number;
        token.date_of_birth = session.date_of_birth;
        token.gender = session.gender;
        token.bio = session.bio;
        token.image = session.image;
        token.email_verified = session.email_verified;
        token.phone_number_verified = session.phone_number_verified;
        token.is_onboarded = session.is_onboarded;
      }

      return token;
    },
    session({ session, token }) {
      if (session.user) {
        session.user.id = token.id as string;
        session.user.name = (token.full_name || token.name) as string;
        session.user.email = token.email as string;
        session.user.username = token.username as string;
        session.user.phone_number = token.phone_number as string;
        session.user.date_of_birth = token.date_of_birth as string;
        session.user.image = token.image as string;
        session.user.gender = token.gender as string;
        session.user.user = token.user as string;
        session.user.bio = token.bio as string;
        session.user.cv_file = token.cv_file as string;
        session.user.service_api_key = token.service_api_key as string;
        session.user.isGoogleAuth = token.isGoogleAuth as boolean;
        session.user.is_staff = token.is_staff as boolean;
        session.user.is_onboarded = token.is_onboarded as boolean;
        session.user.phone_number_verified = token.phone_number_verified as boolean;
        session.user.email_verified = token.email_verified as boolean;
      }
      session.sessionToken = token.accessToken as string;
      return session;
    },
  },
  ...authConfig,
});
