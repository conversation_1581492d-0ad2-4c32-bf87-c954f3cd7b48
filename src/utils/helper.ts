export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export function estimateSpeechDuration(text: string): number {
  const wordsPerMinute: number = 130;
  const words: number = text.split(/\s+/).length;
  return (words / wordsPerMinute) * 60 * 1000;
}

export function parsePhoneNumber(phoneNumber: string | undefined): string {
  if (!phoneNumber) return "";

  const format = phoneNumber.split(" ").join("");

  if (format.startsWith("+")) {
    return format;
  }

  return "+" + format;
}

export function parseNum(num: number | undefined) {
  if (!num) return 0;

  if (num < 1000) return num.toString();

  const units = ["", "K", "M", "B", "T"];
  let unitIndex = 0;
  let scaled = num;

  while (scaled >= 1000 && unitIndex < units.length - 1) {
    scaled /= 1000;
    unitIndex++;
  }

  const formatted = scaled % 1 === 0 ? scaled.toFixed(0) : scaled.toFixed(1);

  return `${formatted}${units[unitIndex]}`;
}
