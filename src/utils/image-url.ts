import { getSupabaseImage } from "./supabase/image";

export function getImageUrl(payload: string, type: "supabase" | "strapi" = "supabase") {
  if (type === "supabase" && payload.startsWith("public/")) {
    return getSupabaseImage().getPublicUrl(payload).data.publicUrl;
  }

  if (type === "strapi" && payload.startsWith("/")) {
    return `${process.env.NEXT_PUBLIC_STRAPI_URL}${payload}`;
  }

  return payload;
}
