import { tryCatch } from "@/utils/try-catch";
import { apiServer } from "@/lib/api-server";
import { TrackEventMixpanel } from "@/lib/mixpanel";


export async function trackFirstFeature(feature: string, path: string) {

  const [res, err] = await tryCatch(
    apiServer({
      method: "GET",
      url: path,
    }),
  );
  
  if (err) {
    return null
  }
  
  const payload = await res.json()

  if (payload.length === 1) {

    const [resUser, errUser] = await tryCatch(
        apiServer({
        method: "GET",
        url: "/api/profile",
        }),
    );

    if (errUser) {
        return null
    }
    const payloadUser = await resUser.json()
    const createdAt = new Date(payloadUser.created_at); 
    const now = new Date();

    const diffMs = now.getTime() - createdAt.getTime(); 
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    await TrackEventMixpanel("time_to_first_value", {
        distinct_id : payloadUser.email,
        user_id : payloadUser.id,
        days_since_signup : diffDays,
        feature : feature
    })

  }


}