export function apiRouteResponse<T>(
  data: T,
  success: boolean = true,
  message: string = "Success",
  status: number = 200,
): Response {
  if (!success) {
    return Response.json(
      {
        success,
        message: message || "Error",
        data: null,
      },
      { status: 400 },
    );
  }

  return Response.json(
    {
      success,
      message,
      data,
    },
    { status },
  );
}
