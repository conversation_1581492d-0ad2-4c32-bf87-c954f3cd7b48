"use client";

import { usePathname } from "next/navigation";
import {
  BookOpenText,
  BriefcaseBusiness,
  ChartLine,
  ChevronRight,
  Cog,
  FileText,
  Gamepad2,
  Plus,
  Search,
  Target,
  Trophy,
  User,
  Users,
} from "lucide-react";

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import { SidebarLink, siteConfig } from "@/lib/config";
import Link from "next/link";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { cn } from "@/utils/cn";

export function SidebarMain({
  title = "Platform",
  links,
}: {
  title?: string;
  links: SidebarLink[];
}) {
  const pathname = usePathname();

  return (
    <SidebarGroup>
      <SidebarGroupLabel>{title}</SidebarGroupLabel>
      <SidebarGroupContent>
        <SidebarMenu>
          {links.map((link, idx) => {
            const icon: Record<SidebarLink["icon"], React.ReactNode> = {
              dashboard: <ChartLine className="size-4" />,
              work: <BriefcaseBusiness className="size-4" />,
              search: <Search className="size-4" />,
              setting: <Cog className="size-4" />,
              cv: <FileText className="size-4" />,
              plus: <Plus className="size-4" />,
              user: <User className="size-4" />,
              users: <Users className="size-4" />,
              book: <BookOpenText className="size-4" />,
              ranking: <Trophy className="size-4" />,
              target: <Target className="size-4" />,
              game: <Gamepad2 className="size-4" />,
            };

            return (
              <Collapsible key={idx} asChild defaultOpen={link.isActive}>
                <SidebarMenuItem>
                  <SidebarMenuButton
                    asChild
                    size="default"
                    isActive={pathname === link.url}
                    tooltip={link.title}
                    disabled={link.disabled}
                    className={cn(
                      link.disabled && "pointer-events-none cursor-not-allowed opacity-50",
                      link.isButton &&
                        "bg-primary text-primary-foreground hover:bg-primary/75 hover:text-primary-foreground transition-colors duration-300",
                    )}
                  >
                    <Link href={link.url}>
                      {icon[link.icon as keyof typeof icon]}
                      <span>{link.title}</span>
                    </Link>
                  </SidebarMenuButton>

                  {link.items && link.items.length ? (
                    <>
                      <CollapsibleTrigger asChild className="hover:bg-primary/20 text-primary">
                        <SidebarMenuAction className="data-[state=open]:rotate-90">
                          <ChevronRight />
                          <span className="sr-only">Toggle</span>
                        </SidebarMenuAction>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <SidebarMenuSub>
                          {link.items &&
                            link.items.map((sub, subIdx) => {
                              return (
                                <SidebarMenuSubItem key={subIdx}>
                                  <SidebarMenuSubButton
                                    asChild
                                    isActive={pathname === sub.url}
                                    className="group gap-2 overflow-hidden"
                                  >
                                    <Link href={sub.url}>
                                      {icon[sub.icon as keyof typeof icon]}
                                      <span>{sub.title}</span>
                                    </Link>
                                  </SidebarMenuSubButton>
                                </SidebarMenuSubItem>
                              );
                            })}
                        </SidebarMenuSub>
                      </CollapsibleContent>
                    </>
                  ) : null}
                </SidebarMenuItem>
              </Collapsible>
            );
          })}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
}
