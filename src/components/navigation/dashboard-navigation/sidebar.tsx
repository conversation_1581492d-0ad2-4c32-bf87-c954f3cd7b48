"use client";

import Link from "next/link";
import Image from "next/image";

import {
  <PERSON><PERSON>,
  <PERSON>bar<PERSON>ontent,
  Sidebar<PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
  useSidebar,
} from "@/components/ui/sidebar";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

import { siteConfig } from "@/lib/config";
import { cn } from "@/utils/cn";

import { SidebarMain } from "./sidebar-main";

import { User } from "next-auth";
import { SidebarLogout } from "./sidebar-logout";
import { getImageUrl } from "@/utils/image-url";
import { Newspaper, Shield } from "lucide-react";

export function DashboardSidebar({
  className,
  links,
  user,
  ...props
}: React.ComponentProps<typeof Sidebar> & {
  links: typeof siteConfig.sidebarLinks;
  user: User | undefined;
}) {
  const { open } = useSidebar();

  return (
    <Sidebar {...props} className={cn("z-20 font-sans", className)} collapsible="icon">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" tooltip={user?.name || ""} asChild>
              <Link
                href="/dashboard"
                className="flex items-center gap-2 px-4 text-lg font-semibold"
              >
                <Avatar className={cn("relative size-8 transition-all", open && "size-12")}>
                  {user?.image && (
                    <Image
                      src={getImageUrl(user.image)}
                      alt={user.name || ""}
                      width={48}
                      height={48}
                      className="absolute size-full object-cover"
                    />
                  )}
                  <AvatarFallback className="bg-gradient-green rounded-none text-white md:text-lg">
                    {user?.name?.charAt(0) || ""}
                  </AvatarFallback>
                </Avatar>

                <div className="font-inter overflow-hidden leading-none">
                  <p className="truncate font-semibold">{user?.name || ""}</p>
                  <p className="text-muted-foreground truncate text-xs">{user?.email || ""}</p>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarMain links={links.mainLinks} />

        {user?.is_staff && (
          <>
            <SidebarSeparator className="mx-0" />
            <SidebarMain title="Admin" links={links.adminLinks} />
            <SidebarMain title="Gamification" links={links.gamificationLinks} />
          </>
        )}
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild size="default">
              <Link href="/news">
                <Newspaper />
                Berita
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton asChild size="default">
              <Link href="/faq">
                <Shield />
                FAQ
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
        <SidebarSeparator className="mx-0" />
        <SidebarLogout />
      </SidebarFooter>
    </Sidebar>
  );
}
