import { Progress } from "../ui/progress";
import { cn } from "@/utils/cn";

export function XpProgress({
  className,
  level,
  xp,
}: {
  className?: string;
  level: number;
  xp: number;
}) {
  const countXP = Math.abs((level - 1) * 100);

  return (
    <div className={cn("relative", className)}>
      <Progress value={Math.abs(countXP - xp)} className="h-5" />
      <p className="text-foreground/20 absolute top-1/2 right-2 z-50 -translate-y-1/2 text-xs leading-none font-medium tabular-nums mix-blend-difference invert">
        {Math.abs(countXP - xp)}/100 XP
      </p>
    </div>
  );
}
