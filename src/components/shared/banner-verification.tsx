"use client";

import { <PERSON><PERSON><PERSON>riangle, Mail, Phone } from "lucide-react";
import { Button } from "../ui/button";
import { motion } from "motion/react";
import { cn } from "@/utils/cn";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { RouteResponse } from "@/types/api-route.type";

export function BannerVerification({ email, phone }: { email: boolean; phone: boolean }) {
  const router = useRouter();
  const { data: session } = useSession();
  const url = `/verification/otp/${session?.user.email}`;

  const handleEmailVerification = async () => {
    const res = await fetch("/api/verification/email", {
      method: "POST",
    });

    const response = (await res.json()) as RouteResponse<string>;

    if (response.success) {
      toast.success(response.data, { description: "Kode verifikasi telah dikirim ke email Anda." });
      router.push(url + "?type=email");
    }
  };

  const handlePhoneVerification = async () => {
    if (!session?.user.phone_number) {
      router.push("/dashboard/setting");
    }

    const res = await fetch("/api/verification/phone", {
      method: "POST",
    });

    const response = (await res.json()) as RouteResponse<string>;

    if (response.success) {
      toast.success(response.data, { description: "Kode verifikasi telah dikirim ke Whatsapp Anda." });
      router.push(url + "?type=phone");
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scaleY: 0 }}
      animate={{ opacity: 1, scaleY: 1 }}
      className="flex origin-top flex-col gap-4 border-b border-amber-200 bg-amber-50 p-4 text-amber-600 md:flex-row md:items-center md:justify-between"
    >
      <div className="flex items-center gap-2">
        <AlertTriangle className="size-4 shrink-0" />
        <p className="text-sm font-medium">Verifikasi email Anda untuk mengakses fitur ini</p>
      </div>

      <div className="flex flex-col gap-2 md:flex-row">
        <Button
          size="sm"
          variant="outline"
          onClick={handleEmailVerification}
          className={cn("bg-muted text-muted-foreground text-xs", {
            hidden: !email,
          })}
        >
          <Mail className="size-3" /> Verifikasi Email
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={handlePhoneVerification}
          className={cn("bg-muted text-muted-foreground text-xs", {
            hidden: !phone,
          })}
        >
          <Phone className="size-3" /> Verifikasi Nomor HP
        </Button>
      </div>
    </motion.div>
  );
}
