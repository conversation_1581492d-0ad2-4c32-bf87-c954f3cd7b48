"use client";

import { motion, Transition } from "motion/react";

type WrapperOpacityProps = React.ComponentProps<"div"> & {
  as?: "div" | "li";
  transition?: Transition;
};

export function WrapperOpacity({
  as = "div",
  transition = { duration: 0.3 },
  children,
  ...props
}: Readonly<WrapperOpacityProps>) {
  const Slot = as === "li" ? "li" : "div";

  const MSlot = motion.create(Slot);

  return (
    // @ts-expect-error error
    <MSlot initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={transition} {...props}>
      {children}
    </MSlot>
  );
}
