import { Check } from "lucide-react";
import Image from "next/image";
import { RiLockFill } from "@remixicon/react";

import { cn } from "@/utils/cn";
import { GamificationBadge } from "@/types/response.type";

export function BadgeProgress({
  as,
  className,
  data,
}: {
  className?: string;
  as?: "li" | "div";
  data: GamificationBadge;
}) {
  const Slot = as === "li" ? "li" : "div";

  return (
    <Slot
      className={cn("flex aspect-square flex-col items-center justify-center gap-4", className, {
        "opacity-50": !data.earned_at,
      })}
    >
      <div className="relative size-fit">
        <div
          className={cn(
            "border-background absolute top-0 right-0 flex size-6 translate-x-1/4 -translate-y-1/4 items-center justify-center rounded-full border-2 md:size-8",
            {
              "bg-gray-600": !data.earned_at,
              "bg-green-500": data.earned_at,
            },
          )}
        >
          {data.earned_at ? (
            <Check className="text-background size-4" />
          ) : (
            <RiLockFill className="text-background size-4" />
          )}
        </div>
        <Image
          src={data.icon_url}
          alt={data.name}
          width={100}
          height={100}
          className="size-[100px] rounded-full object-cover"
        />
      </div>

      <div>
        <p className="line-clamp-1 max-w-[25ch] text-center">{data.name}</p>
        <p className="text-muted-foreground line-clamp-1 max-w-[30ch] text-center text-xs">
          {data.description}
        </p>
      </div>
    </Slot>
  );
}
