"use client";

import { useInView } from "motion/react";
import { useRef, useState } from "react";
import { AlertCircle, ChevronDown } from "lucide-react";

import { Button } from "./button";

import { cn } from "@/utils/cn";
import { ISchedule } from "@/types/response.type";

export type TimeSlot = {
  time: string;
  available: boolean;
};

type TimePickerProps = {
  name: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  times: ISchedule[] | null;
  isLoading?: boolean;
};

export function TimePicker({
  name,
  times,
  required,
  disabled,
  className,
  isLoading,
}: TimePickerProps) {
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, { amount: "all" });

  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const [selectedId, setSelectedId] = useState<number | undefined>(undefined);

  const handleTimeSelect = (time: string, available: boolean) => {
    if (available) {
      setSelectedTime(time);
    }
  };

  return (
    <div
      className={cn(
        "relative flex flex-col gap-4",
        "has-[data-slot=time-picker]:disabled:opacity-80",
        className,
      )}
    >
      <input
        data-slot="time-picker"
        name={name}
        className="sr-only"
        defaultValue={selectedId}
        disabled={disabled}
        required={required}
      />

      <div className="flex items-center justify-between">
        <p className="text-muted-foreground flex items-center gap-1 text-xs leading-none">
          <AlertCircle className="size-3" />
          <span>Pilih waktu yang tersedia (jeda 30 menit)</span>
        </p>
      </div>

      <div
        className={cn(
          "no-scrollbar grid h-[200px] grid-cols-6 gap-3 overflow-auto",
          isLoading ? "animate-pulse" : "",
        )}
      >
        <div className="pointer-events-none sticky top-0 col-span-full mb-2 w-full">
          <div className="from-card absolute top-0 left-0 z-20 h-10 w-full bg-gradient-to-b to-transparent"></div>
        </div>
        {!isLoading &&
          times?.map((slot) => {
            return (
              <Button
                type="button"
                key={slot.id}
                variant="outline"
                className={cn(
                  "disabled:text-destructive disabled:border-destructive/50 h-12 text-sm font-medium transition-colors",
                  slot.remaining_capacity === 0
                    ? "cursor-not-allowed border-red-200 bg-red-50 text-red-400 hover:bg-red-50"
                    : selectedTime === slot.start_time
                      ? "bg-gradient-purple text-white hover:text-white/90"
                      : "",
                )}
                onClick={() => {
                  handleTimeSelect(slot.start_time, slot.remaining_capacity !== 0);
                  setSelectedId(slot.id);
                }}
                disabled={slot.booked_sessions === 1}
              >
                {slot.start_time}
              </Button>
            );
          })}
        <div ref={ref} className="col-span-full"></div>
        <div className="pointer-events-none sticky bottom-0 col-span-full mt-2 w-full">
          <div className="from-card absolute bottom-0 left-0 z-20 h-10 w-full bg-gradient-to-t to-transparent"></div>
        </div>
      </div>

      <Button
        type="button"
        size="icon"
        variant="outline"
        className={cn(
          "absolute bottom-6 left-1/2 -translate-x-1/2 rounded-full",
          isInView ? "hidden" : "",
        )}
        onClick={() => {
          if (ref.current) {
            ref.current.scrollIntoView({ behavior: "smooth" });
          }
        }}
      >
        <ChevronDown />
      </Button>
    </div>
  );
}
