import { FaFileAlt, FaRocket } from "react-icons/fa";
import { FaBriefcase, FaChartLine } from "react-icons/fa6";
import { IoChatbubbles } from "react-icons/io5";

import { Card } from "@/components/ui/card";
import { StrapiHomeStepsSection } from "@/types/strapi.type";

const icons = {
  file: <FaFileAlt className="size-8 text-primary" />,
  briefcase: <FaBriefcase className="size-8 text-primary" />,
  chat: <IoChatbubbles className="size-8 text-primary" />,
  chart: <FaChartLine className="size-8 text-primary" />,
  rocket: <FaRocket className="size-8 text-primary" />,
};

export function HomeStepsView({ data: tutorialSection }: { data: StrapiHomeStepsSection }) {
  return (
    <section className="flex items-center justify-center overflow-hidden bg-gray-50 px-6 font-sans dark:bg-gray-900">
      {tutorialSection && (
        <div className="container flex w-full flex-col items-center justify-center gap-8 px-6 py-24 md:gap-24 md:px-0 md:py-30">
          <div className="w-fit space-y-4 text-center">
            <h2 className="clamp-[text,2xl,4xl] font-bold text-gray-900 dark:text-white">
              {tutorialSection.title}
            </h2>
            <p className="clamp-[text,sm,lg] mx-auto max-w-[80ch] text-balance text-muted-foreground dark:text-gray-400">
              {tutorialSection.description}
            </p>
          </div>

          <div className="w-full">
            <div className="grid grid-cols-1 gap-3 gap-y-10 md:grid-cols-5 md:gap-6">
              {tutorialSection.cards.map((step) => (
                <div key={step.id} className="flex flex-col items-center gap-7">
                  <div className="flex h-16 w-16 items-center justify-center rounded-full bg-[#EDE9FE] text-xl font-bold text-primary">
                    <p>{step.number}</p>
                  </div>
                  <Card className="space-y-1 border-0 bg-white p-6 text-center shadow-none transition-shadow hover:shadow-lg md:space-y-9 dark:bg-gray-800">
                    <div className="flex justify-center">
                      <div className="">{icons[step.icon]}</div>
                    </div>
                    <div className="flex flex-col gap-0.5">
                      <h3 className="clamp-[text,sm,lg] font-semibold text-gray-900 dark:text-white">
                        {step.title}
                      </h3>

                      <p className="clamp-[text,xs,sm] leading-relaxed text-muted-foreground dark:text-gray-400">
                        {step.description}
                      </p>
                    </div>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </section>
  );
}
