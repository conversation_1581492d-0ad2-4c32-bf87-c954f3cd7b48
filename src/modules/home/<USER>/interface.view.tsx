import Image from "next/image";

import { Card } from "@/components/ui/card";
import { StrapiHomeInterfaceSection } from "@/types/strapi.type";
import { getImageUrl } from "@/utils/image-url";

export function HomeInterfaceView({
  data: interfaceSection,
}: {
  data: StrapiHomeInterfaceSection;
}) {
  return (
    <section className="flex items-center justify-center overflow-hidden bg-gray-50 px-6 font-sans dark:bg-gray-900">
      {interfaceSection && (
        <div className="container flex w-full flex-col items-center justify-center gap-8 px-6 py-24 md:gap-12 md:px-0 md:py-30">
          <div className="w-fit space-y-4 text-center">
            <h2 className="clamp-[text,2xl,4xl] font-bold text-gray-900 dark:text-white">
              {interfaceSection.title}
            </h2>
            <p className="clamp-[text,sm,lg] text-muted-foreground mx-auto max-w-[60ch] text-balance dark:text-gray-400">
              {interfaceSection.description}
            </p>
          </div>

          <div className="grid w-full grid-cols-1 gap-6 md:grid-cols-2 md:gap-8">
            {interfaceSection.cards.map((feature) => (
              <Card
                key={feature.id}
                className="group max-h-xs md:max-h-xl overflow-hidden border-none p-0 shadow-lg transition-shadow duration-300 hover:shadow-xl dark:bg-gray-800"
              >
                <div className="relative aspect-video overflow-hidden">
                  <Image
                    src={getImageUrl(feature.image.url, "strapi")}
                    alt={feature.title}
                    height={320}
                    width={640}
                    className="object-cover transition-transform duration-500 ease-in-out group-hover:scale-110"
                  />
                </div>

                <div className="space-y-3 p-6">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {feature.title}
                  </h3>
                  <p className="text-muted-foreground leading-relaxed dark:text-gray-400">
                    {feature.description}
                  </p>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}
    </section>
  );
}
