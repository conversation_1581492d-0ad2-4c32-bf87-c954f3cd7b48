import Link from "next/link";
import { Fa<PERSON>rrowR<PERSON>, Fa<PERSON><PERSON>, FaQuest<PERSON> } from "react-icons/fa6";
import { PiSmileyXEyesFill } from "react-icons/pi";

import { Card } from "@/components/ui/card";
import { StrapiHomeProblemSection } from "@/types/strapi.type";

const icons = {
  eyes: <PiSmileyXEyesFill className="size-6 text-red-500" />,
  question: <FaQuestion className="size-6 text-yellow-500" />,
  ban: <FaBan className="size-6 text-blue-500" />,
};

export function HomeProblemView({ data: problemSection }: { data: StrapiHomeProblemSection }) {
  return (
    <section className="flex items-center justify-center overflow-hidden px-6 font-sans">
      {problemSection && (
        <div className="container flex w-full flex-col items-center justify-center gap-8 px-6 py-24 md:gap-12 md:px-0 md:py-30">
          <div className="w-fit space-y-4 text-center">
            <h2 className="clamp-[text,2xl,4xl] font-bold text-gray-900">{problemSection.title}</h2>
            <p className="clamp-[text,sm,lg] text-muted-foreground mx-auto max-w-2xl">
              {problemSection.description}
            </p>
          </div>

          <div className="grid w-full grid-cols-1 gap-6 md:grid-cols-3 md:gap-8">
            {problemSection?.cards?.map((card) => (
              <Card
                key={card.id}
                className="relative space-y-0 border-0 bg-[#F9FAFB] p-6 shadow-none"
              >
                <div className="flex flex-row items-center gap-4 md:flex-col md:items-start">
                  <div className="flex">
                    <div className={`rounded-full p-3 ${card.bgColor}`}>{icons[card.icon]}</div>
                  </div>
                  <h3 className="clamp-[text,lg,xl] font-semibold text-gray-900">{card.title}</h3>
                </div>
                <p className="clamp-[text,sm,lg] text-muted-foreground pt-0 leading-relaxed">
                  {card.description}
                </p>
              </Card>
            ))}
          </div>

          <Card className="grid w-full space-y-6 border-0 bg-[#F9FAFB] px-7 py-16 shadow-none md:grid-cols-2">
            <div className="flex max-w-2xl flex-col gap-4">
              <h3 className="clamp-[text,xl,3xl] font-bold text-gray-900 dark:text-white">
                {problemSection.problemHighlight.title}
              </h3>

              <div className="mx-auto max-w-2xl space-y-4 text-left">
                <p className="text-muted-foreground leading-relaxed dark:text-gray-400">
                  {problemSection.problemHighlight.description}
                </p>
              </div>

              <div className="pt-4">
                <Link
                  href={problemSection.problemHighlight.cta_link}
                  className="group text-primary flex items-center gap-1 font-semibold transition-colors hover:text-[#5c4389]"
                >
                  {problemSection.problemHighlight.cta_text}
                  <FaArrowRight className="h-4 w-4 stroke-[3] transition-transform group-hover:translate-x-1" />
                </Link>
              </div>
            </div>
          </Card>
        </div>
      )}
    </section>
  );
}
