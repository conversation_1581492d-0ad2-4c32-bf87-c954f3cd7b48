import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Benefit, StrapiHomePricingSection } from "@/types/strapi.type";
import { Check, X } from "lucide-react";

const FeatureItem = ({ feature }: { feature: Benefit }) => (
  <div className="flex items-center gap-3">
    <div
      className={`flex flex-shrink-0 items-center justify-center ${
        feature.isIncluded ? "text-green-400" : "text-red-400"
      }`}
    >
      {feature.isIncluded ? <Check className="h-5 w-5" /> : <X className="h-5 w-5" />}
    </div>
    <span
      className={`text-sm ${
        feature.isIncluded ? "text-gray-900 dark:text-white" : "text-gray-400 dark:text-gray-500"
      }`}
    >
      {feature.label}
    </span>
  </div>
);

export function HomePricingView({ data: pricingSection }: { data: StrapiHomePricingSection }) {
  return (
    <section className="flex items-center justify-center overflow-hidden bg-white font-sans dark:bg-gray-900">
      {pricingSection && (
        <div className="container flex w-full flex-col items-center justify-center gap-8 px-6 py-24 md:px-0 md:py-30">
          <div className="max-w-3xl space-y-4 text-center">
            <h2 className="clamp-[text,2xl,4xl] font-bold text-gray-900 dark:text-white">
              {pricingSection.title}
            </h2>
            <p className="clamp-[text,sm,lg] text-muted-foreground mx-auto max-w-2xl dark:text-gray-400">
              {pricingSection.description}
            </p>
          </div>

          <div className="grid w-full max-w-4xl grid-cols-1 gap-6 md:grid-cols-2">
            {pricingSection.cards.map((plan) => (
              <Card
                key={plan.id}
                className={`relative border p-6 shadow-sm transition-shadow hover:shadow-md md:p-8 dark:bg-gray-800 ${
                  plan.isPopuler
                    ? "border-purple-200 bg-gradient-to-br from-[#F5F3FF] to-[#FDF2F8] dark:border-purple-700 dark:from-purple-700 dark:to-purple-400"
                    : "border-gray-200 bg-white dark:border-gray-700"
                }`}
              >
                {plan.isPopuler && (
                  <Badge className="absolute top-0 right-0 rounded-tl-none rounded-tr-lg rounded-br-none rounded-bl-lg bg-purple-600 px-2 py-1 text-xs font-extrabold text-white hover:bg-purple-700">
                    POPULER
                  </Badge>
                )}

                <div className="flex flex-col gap-6">
                  <div className="space-y-2 text-center">
                    <h3 className="text-xl font-bold text-gray-900 md:text-2xl dark:text-white">
                      {plan.title}
                    </h3>
                    <p className="text-muted-foreground text-sm dark:text-gray-400">
                      {plan.description}
                    </p>
                  </div>

                  <div className="text-center">
                    <div className="flex items-baseline justify-center gap-2">
                      <span className="text-3xl font-bold text-gray-900 md:text-4xl dark:text-white">
                        Rp {plan.price.toLocaleString("id-ID")}
                      </span>
                      <span className="text-gray-500 dark:text-gray-400">/ {plan.period}</span>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {plan.benefits.map((benefit) => (
                      <FeatureItem key={benefit.id} feature={benefit} />
                    ))}
                  </div>

                  <Button size="lg" className="mt-4 w-full">
                    {plan.buyButton}
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}
    </section>
  );
}
