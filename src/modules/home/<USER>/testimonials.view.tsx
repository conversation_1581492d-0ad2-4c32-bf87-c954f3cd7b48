import { FaStar, FaStarHalfAlt } from "react-icons/fa";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card } from "@/components/ui/card";
import { StrapiHomeTestimoniSection } from "@/types/strapi.type";
import { getImageUrl } from "@/utils/image-url";

const StarRating = ({ rating }: { rating: number }) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;

  return (
    <div className="flex gap-1">
      {[...Array(fullStars)].map((_, index) => (
        <FaStar key={index} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
      ))}
      {hasHalfStar && <FaStarHalfAlt className="h-5 w-5 fill-yellow-400 text-yellow-400" />}
    </div>
  );
};

export function HomeTestimonialsView({
  data: testimoniSection,
}: {
  data: StrapiHomeTestimoniSection;
}) {
  return (
    <section className="flex items-center justify-center overflow-hidden bg-gray-50 px-6 font-sans dark:bg-gray-900">
      {testimoniSection && (
        <div className="container flex w-full flex-col items-center justify-center gap-8 px-6 py-24 md:px-0 md:py-30">
          <div className="max-w-3xl space-y-4 text-center">
            <h2 className="clamp-[text,2xl,4xl] font-bold text-gray-900 dark:text-white">
              {testimoniSection.title}
            </h2>
            <p className="clamp-[text,sm,lg] text-muted-foreground mx-auto max-w-2xl dark:text-gray-400">
              {testimoniSection.description}
            </p>
          </div>

          <div className="grid w-full grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {testimoniSection.cards.map((testimonial) => {
              const initials = testimonial.name
                .split(" ")
                .map((word) => word[0])
                .join("")
                .toUpperCase();

              const avatarUrl = testimonial.avatar?.url
                ? getImageUrl(testimonial.avatar.url, "strapi")
                : undefined;

              return (
                <Card
                  key={testimonial.id}
                  className="border-0 bg-white p-6 shadow-2xs transition-shadow hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
                >
                  <div className="flex flex-col gap-4">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-12 w-12 flex-shrink-0">
                        <AvatarImage src={avatarUrl} alt={testimonial.name} />
                        <AvatarFallback className="dark:bg-muted-foreground bg-gray-200 font-semibold text-gray-700 dark:text-gray-300">
                          {initials}
                        </AvatarFallback>
                      </Avatar>
                      <div className="min-w-0 flex-1">
                        <h4 className="text-base font-semibold text-gray-900 dark:text-white">
                          {testimonial.name}
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {testimonial.position}
                        </p>
                      </div>
                    </div>

                    <p className="text-sm leading-relaxed text-gray-700 dark:text-gray-300">
                      &quot;{testimonial.testimoni}&quot;
                    </p>

                    <div className="flex items-center gap-2">
                      <StarRating rating={testimonial.start} />
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>
        </div>
      )}
    </section>
  );
}
