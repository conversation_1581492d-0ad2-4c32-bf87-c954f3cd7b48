import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-icons/fa";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { StrapiHomeCommunitySection } from "@/types/strapi.type";
import { getImageUrl } from "@/utils/image-url";

export function HomeCommunityView({
  data: communitySection,
}: {
  data: StrapiHomeCommunitySection;
}) {
  return (
    <section className="flex items-center justify-center overflow-hidden bg-white px-6 font-sans dark:bg-gray-900">
      <div className="container flex w-full flex-col items-center justify-center gap-8 px-6 py-24 md:px-0 md:py-30">
        <div className="max-w-3xl space-y-4 text-center">
          <h2 className="clamp-[text,2xl,4xl] font-bold text-gray-900 dark:text-white">
            {communitySection.title}
          </h2>
          <p className="clamp-[text,sm,lg] mx-auto max-w-2xl text-muted-foreground dark:text-gray-400">
            {communitySection.description}
          </p>
        </div>

        <Card className="w-full max-w-full border-0 bg-gradient-to-br from-[#F5F3FF] to-[#FDF2F8] p-6 shadow-lg md:p-8 dark:from-purple-600 dark:to-gray-900">
          <div className="grid items-center gap-8 md:grid-cols-2">
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-gray-900 md:text-3xl dark:text-white">
                {communitySection.card.title}
              </h3>
              <p className="text-base text-muted-foreground md:text-lg dark:text-gray-400">
                {communitySection.card.description}
              </p>
              <div className="flex flex-col gap-4 sm:flex-row">
                <Button
                  size="lg"
                  className="flex items-center gap-2 bg-green-500 px-6 py-3 text-white hover:bg-green-600"
                  asChild
                >
                  <a href={communitySection.card.joinWaLink} target="_blank">
                    <FaWhatsapp />
                    {communitySection.card.joinWaText}
                  </a>
                </Button>
                <Button
                  size="lg"
                  className="flex items-center gap-2 bg-indigo-500 px-6 py-3 text-white hover:text-white"
                  asChild
                >
                  <a href={communitySection.card.joinDcLink} target="_blank">
                    <FaDiscord />
                    {communitySection.card.joinDcText}
                  </a>
                </Button>
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              {communitySection.cards.map((testimonial) => {
                const initials = testimonial.name
                  .split(" ")
                  .map((word) => word[0])
                  .join("")
                  .toUpperCase();

                const avatarUrl = testimonial.avatar?.url
                  ? getImageUrl(testimonial.avatar.url, "strapi")
                  : undefined;

                return (
                  <Card
                    key={testimonial.id}
                    className="border-0 bg-white p-4 shadow-2xs dark:bg-gray-700"
                  >
                    <div className="flex gap-3">
                      <Avatar className="h-10 w-10 flex-shrink-0">
                        <AvatarImage src={avatarUrl} alt={testimonial.name} />
                        <AvatarFallback className="bg-gray-200 font-semibold text-gray-700 dark:bg-muted-foreground dark:text-gray-300">
                          {initials}
                        </AvatarFallback>
                      </Avatar>
                      <div className="min-w-0 flex-1">
                        <div className="mb-2 flex flex-col gap-1">
                          <h4 className="text-sm font-semibold text-gray-900 dark:text-white">
                            {testimonial.name}
                          </h4>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {testimonial.position}
                          </p>
                        </div>
                        <p className="text-sm leading-relaxed text-muted-foreground dark:text-gray-300">
                          &quot;{testimonial.testimoni}&quot;
                        </p>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>
          </div>

          <div className="mt-8 text-center">
            <p className="text-lg font-semibold text-indigo-600 md:text-xl dark:text-indigo-300">
              {communitySection.card.caption}
            </p>
          </div>
        </Card>
      </div>
    </section>
  );
}
