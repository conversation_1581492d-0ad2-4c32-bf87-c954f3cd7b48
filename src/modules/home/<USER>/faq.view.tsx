"use client";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { StrapiHomeFaqSection } from "@/types/strapi.type";

export function HomeFaqView({ data: faqSection }: { data: StrapiHomeFaqSection }) {
  return (
    <section className="flex items-center justify-center overflow-hidden bg-gray-50 font-sans dark:bg-gray-900">
      {faqSection && (
        <div className="container flex w-full flex-col items-center justify-center gap-6 px-6 py-24 md:px-0 md:py-30">
          <div className="w-full max-w-3xl space-y-6">
            <div className="mb-12 space-y-4 text-center">
              <h2 className="clamp-[text,2xl,4xl] font-bold text-gray-900 dark:text-white">
                {faqSection.title}
              </h2>
              <p className="clamp-[text,sm,lg] text-muted-foreground dark:text-gray-400">
                {faqSection?.description}
              </p>
            </div>

            <Accordion type="single" collapsible className="space-y-4">
              {faqSection.Accordion.map((item) => (
                <AccordionItem
                  key={item.id}
                  value={`item-${item.id}`}
                  className="rounded-lg border-0 bg-white px-6 py-2 shadow-2xs hover:bg-purple-50 dark:bg-gray-800 dark:hover:bg-gray-700/50"
                >
                  <AccordionTrigger className="rounded-lg px-0 py-4 text-left hover:no-underline [&[data-state=open]>svg]:rotate-180">
                    <span className="text-lg font-medium text-gray-900 dark:text-white">
                      {item.question}
                    </span>
                  </AccordionTrigger>
                  <AccordionContent className="pb-4">
                    <div className="pt-2">
                      <p className="leading-relaxed text-muted-foreground dark:text-gray-300">
                        {item.answer}
                      </p>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div>
      )}
    </section>
  );
}
