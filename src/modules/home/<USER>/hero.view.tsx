import Link from "next/link";

import { Card } from "@/components/ui/card";
import { buttonVariants } from "@/components/ui/button";

import { StrapiHomeHeroSection } from "@/types/strapi.type";
import Image from "next/image";
import { getImageUrl } from "@/utils/image-url";

export function HomeHeroView({ data: heroSection }: { data: StrapiHomeHeroSection }) {
  return (
    <section
      id="hero"
      className="col-span-full flex w-full items-center justify-center bg-gradient-to-br from-[#F5F3FF] to-[#FDF2F8] px-6 pt-30 pb-50 font-sans dark:bg-purple-800 dark:from-purple-600 dark:to-gray-900"
      aria-label="<PERSON><PERSON>han Interview <PERSON><PERSON><PERSON> AI"
      lang="id"
    >
      <div className="@container/hero container flex flex-col-reverse items-center justify-center gap-6 px-6 md:grid md:grid-cols-2 md:flex-row md:px-0">
        <header className="space-y-4 self-start">
          <h1 className="clamp-[text,3xl,5xl] leading-normal font-bold md:max-w-2xl">
            {heroSection.title}
          </h1>
          <p className="clamp-[text,sm,3xl] gap-4 md:mb-12">{heroSection.description}</p>
          <nav aria-label="Aksi utama">
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
              <Link
                href="/login"
                className={buttonVariants({
                  className: "w-full px-6 text-center sm:w-auto",
                })}
              >
                {heroSection.buttonStart}
              </Link>
              <Link
                href="/login"
                className={buttonVariants({
                  variant: "outline",
                  className:
                    "border-primary text-primary hover:text-primary/80 w-full bg-transparent px-6 text-center sm:w-auto dark:text-purple-100",
                })}
              >
                {heroSection.buttonCommunity}
              </Link>
            </div>
          </nav>
        </header>

        <Card className="relative aspect-video h-auto w-full p-0" aria-label="Ilustrasi AI Interview">
          <div className="relative size-full overflow-hidden rounded-xl">
            {heroSection.poster?.mime.startsWith("image") && (
              <Image
                src={getImageUrl(heroSection.poster?.url || "", "strapi")}
                alt={heroSection.poster?.alternativeText || ""}
                fill
                sizes="(max-width: 768px) 100vw, 80vw"
                className="object-cover"
              />
            )}

            {heroSection.poster?.mime.startsWith("video") && (
              <video
                src={getImageUrl(heroSection.poster?.url || "", "strapi")}
                autoPlay
                loop
                muted
                playsInline
                disablePictureInPicture
                disableRemotePlayback
                className="size-full object-cover"
              />
            )}
          </div>

          <div className="bg-primary text-primary-foreground absolute right-0 bottom-0 m-4 rounded-full px-3 py-1">
            {heroSection.caption}
          </div>
        </Card>
      </div>
    </section>
  );
}
