import { IoSpeedometer } from "react-icons/io5";
import { FaBrain, FaChartSimple, FaFileVideo, FaShieldHalved, FaVideo } from "react-icons/fa6";

import { Card } from "@/components/ui/card";
import { StrapiHomeFeatureSection } from "@/types/strapi.type";

const icons = {
  video: <FaVideo className="text-primary size-6" />,
  chart: <FaChartSimple className="text-primary size-6" />,
  brain: <FaBrain className="text-primary size-6" />,
  file: <FaFileVideo className="text-primary size-6" />,
  shield: <FaShieldHalved className="text-primary size-6" />,
  speedo: <IoSpeedometer className="text-primary size-6" />,
};

export function HomeFeaturesView({ data: featureSection }: { data: StrapiHomeFeatureSection }) {
  return (
    <section className="font-san flex items-center justify-center overflow-hidden bg-white px-6 dark:bg-gray-900">
      {featureSection && (
        <div className="container flex w-full flex-col items-center justify-center gap-8 px-6 py-24 md:gap-12 md:px-0 md:py-30">
          <div className="w-fit space-y-7 text-center">
            <h2 className="clamp-[text,2xl,4xl] font-bold text-gray-900 dark:text-white">
              {featureSection.title}
            </h2>
            <p className="clamp-[text,sm,lg] mx-auto max-w-[80ch] text-balance text-muted-foreground dark:text-gray-400">
              {featureSection.description}
            </p>
          </div>

          <div className="grid w-full grid-cols-2 gap-3 pt-7 md:grid-cols-3 md:gap-8">
            {featureSection.cards.map((feature) => (
              <Card
                key={feature.id}
                className="space-y-1 border-2 border-gray-100 bg-white p-3 shadow-none transition-shadow hover:shadow-lg md:p-6 dark:border-gray-800 dark:bg-gray-800"
              >
                <div className="flex justify-start">
                  <div className={`rounded-full bg-[#EDE9FE] p-2 md:p-4`}>
                    {icons[feature.icon]}
                  </div>
                </div>
                <div className="space-y-1">
                  <h3 className="clamp-[text,xs,lg] font-semibold text-gray-900 dark:text-white">
                    {feature.title}
                  </h3>
                  <p className="clamp-[text,xs,lg] leading-relaxed text-muted-foreground dark:text-gray-400">
                    {feature.description}
                  </p>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}
    </section>
  );
}
