import Link from "next/link";

import { Button } from "@/components/ui/button";
import { StrapiHomeCtaSection } from "@/types/strapi.type";

export function HomeCtaView({ data: ctaSection }: { data: StrapiHomeCtaSection }) {
  return (
    <section className="bg-gradient-purple flex items-center justify-center overflow-hidden font-sans">
      {ctaSection && (
        <div className="container flex w-full flex-col items-center justify-center gap-8 px-6 py-16 md:px-0 md:py-24">
          <div className="max-w-4xl space-y-6 text-center">
            <h2 className="clamp-[text,2xl,4xl] leading-tight font-bold text-white">
              {ctaSection.title}
            </h2>
            <p className="mx-auto max-w-2xl text-lg leading-relaxed text-purple-100 md:text-xl">
              {ctaSection.description}
            </p>
          </div>

          <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
            <Button
              size="lg"
              className="rounded-lg bg-white px-8 py-6 text-lg font-semibold text-purple-700 shadow-lg transition-all duration-300 hover:bg-purple-700 hover:text-white hover:shadow-xl"
              asChild
            >
              <Link href="/register">{ctaSection.startButton}</Link>
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="rounded-lg border-2 border-white bg-transparent px-8 py-6 text-lg font-semibold text-white shadow-lg transition-all duration-300 hover:bg-white/10 hover:text-white hover:shadow-xl"
              asChild
            >
              <Link href="/">{ctaSection.learnButton}</Link>
            </Button>
          </div>
        </div>
      )}
    </section>
  );
}
