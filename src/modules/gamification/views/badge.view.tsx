import Link from "next/link";
import Image from "next/image";
import { Suspense } from "react";
import { Ri<PERSON><PERSON><PERSON>ill, RiError<PERSON>arningFill, Ri<PERSON>ockFill } from "@remixicon/react";
import { Check } from "lucide-react";

import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { tryCatch } from "@/utils/try-catch";
import { apiServer } from "@/lib/api-server";
import { Skeleton } from "@/components/ui/skeleton";
import { GamificationBadge } from "@/types/response.type";
import { cn } from "@/utils/cn";
import { BadgeProgress } from "@/components/shared/badge-progress";

export function GamificationBadgeView() {
  return (
    <Card className="rounded-none border-x-0 border-t-0 shadow-none sm:rounded-xl sm:border-x sm:border-t">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="text-primary flex items-center gap-2">
            <RiAwardFill className="size-4" />
            <h3 className="clamp-[text,sm,base] leading-none font-bold">Badge Kamu</h3>
          </div>

          <Link
            href="/dashboard/gamification/badge"
            className="text-primary hover:text-primary/80 h-fit text-xs font-medium transition-opacity"
          >
            Lihat Semua
          </Link>
        </div>
      </CardHeader>

      <Suspense fallback={<Loading />}>
        <Badges />
      </Suspense>
    </Card>
  );
}

async function Badges() {
  const [res, err] = await tryCatch(
    apiServer({
      method: "GET",
      url: "/api/gamification/badges-user",
      cache: "no-store",
    }),
  );

  if (err || !res.ok) {
    return (
      <CardContent className="flex h-24 items-center justify-center">
        <div className="flex flex-col items-center justify-center">
          <RiErrorWarningFill className="text-muted-foreground size-10" />

          <p className="text-muted-foreground text-lg font-semibold">Tidak ada badge tersedia!</p>
        </div>
      </CardContent>
    );
  }

  const data = (await res.json()) as Array<GamificationBadge>;

  return (
    <CardContent>
      <ul className="grid grid-cols-2 md:gap-6 @lg/dashboard:grid-cols-3">
        {data.map((item, idx) => {
          return <BadgeProgress key={idx} as="li" data={item} />;
        })}
      </ul>
    </CardContent>
  );
}

function Loading() {
  return (
    <CardContent className="grid grid-cols-1 gap-6 @lg/dashboard:grid-cols-3">
      {Array.from({ length: 6 }).map((_, index) => (
        <Skeleton key={index} className="aspect-square" />
      ))}
    </CardContent>
  );
}
