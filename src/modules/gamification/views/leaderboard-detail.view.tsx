import { User2 } from "lucide-react";
import { Ri<PERSON>rophy<PERSON>ill, RiVipCrown2Fill } from "@remixicon/react";

import { CardLeaderboard } from "../components/card-leaderboard";

import { Badge } from "@/components/ui/badge";
import { Spotlight } from "@/components/ui/spotlight";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { auth } from "@/auth";
import { apiServer } from "@/lib/api-server";
import { getImageUrl } from "@/utils/image-url";
import { tryCatch } from "@/utils/try-catch";
import { parseNum } from "@/utils/helper";

import { GamificationLeaderboard } from "@/types/response.type";

export async function GamificationLeaderboardDetailView() {
  const session = await auth();
  const [res, err] = await tryCatch(
    apiServer({
      method: "GET",
      url: "/api/gamification/leaderboard",
      cache: "default",
      next: { revalidate: 60 * 15, tags: ["gamification-leaderboard"] },
    }),
  );

  if (err || !res.ok) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Card className="w-full max-w-md border-0 shadow-lg">
          <CardHeader className="text-center">
            <div className="bg-muted mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full">
              <RiTrophyFill className="text-muted-foreground h-8 w-8" />
            </div>
            <CardTitle className="text-xl font-semibold">Papan Peringkat</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-muted-foreground text-sm">
              Tidak ada papan peringkat tersedia saat ini
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const data = (await res.json()) as Array<GamificationLeaderboard>;

  const rank1 = data.shift();
  const rank2 = data.shift();
  const rank3 = data.shift();

  const checkIsTop3 = () => {
    const topUsername = [
      rank1?.user_profile.username,
      rank2?.user_profile.username,
      rank3?.user_profile.username,
    ];

    if (topUsername.includes(session?.user.username)) return true;
    return false;
  };

  return (
    <div className="container mx-auto flex-1 space-y-6">
      <div className="flex flex-col items-center justify-center gap-4 p-4 md:p-6">
        <div className="flex flex-col items-center justify-center">
          <div className="mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-br from-yellow-400 to-orange-500 shadow-lg">
            <RiVipCrown2Fill className="h-10 w-10 text-white" />
          </div>
          <h1 className="clamp-[text,lg,2xl] font-bold tracking-tight">Leaderboard</h1>
        </div>

        <div className="grid grid-cols-3 items-end gap-4 md:gap-6">
          {/* Rank 2 */}
          <div className="bg-border relative row-span-2 mt-5 overflow-hidden rounded-xl p-px shadow-xl">
            <Spotlight className="from-primary to-primary bg-radial via-zinc-800 blur-3xl" />

            <div className="relative h-full w-full rounded-xl bg-gradient-to-br from-slate-100 to-slate-200 py-6">
              <div className="flex flex-col items-center justify-center gap-4 px-6">
                <div className="relative size-fit">
                  <Avatar className="clamp-[size,12,20] outline-4 outline-slate-400">
                    <AvatarImage src={getImageUrl(rank2?.user_profile.profile_picture || "")} />
                    <AvatarFallback className="bg-gradient-orange">
                      <User2 className="size-6 text-white md:size-8" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute bottom-0 left-1/2 min-w-6 -translate-x-1/2 translate-y-1/2 rounded-xl bg-slate-400 px-1 text-center font-medium text-slate-700">
                    {rank2?.level}
                  </div>
                </div>

                <div className="flex flex-col items-center gap-6">
                  <div className="flex flex-col items-center gap-3">
                    <div>
                      <h2 className="clamp-[text,base,lg] line-clamp-1 max-w-[10ch] text-center leading-none font-semibold">
                        {rank2?.user_profile.username === session?.user.username ? (
                          <span className="bg-gradient-purple bg-clip-text text-transparent">
                            You
                          </span>
                        ) : (
                          rank2?.user_profile.full_name
                        )}
                      </h2>
                      <p className="text-muted-foreground text-center text-xs md:text-sm">
                        @{rank3?.user_profile.username}
                      </p>
                    </div>
                    <Badge className="bg-gradient-silver text-foreground">
                      {parseNum(rank2?.xp)} XP
                    </Badge>
                  </div>
                  <div className="clamp-[text,2xl,5xl] bg-slate-400 bg-clip-text font-bold text-transparent">
                    2
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Rank 1 */}
          <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-yellow-100 to-yellow-200 p-px shadow-xl">
            <Spotlight className="from-primary bg-radial via-yellow-500 to-yellow-300 blur-3xl" />
            <div className="relative h-full w-full rounded-xl bg-yellow-100 py-6">
              <div className="flex flex-col items-center justify-center gap-4 px-6">
                <div className="relative size-fit">
                  <Avatar className="clamp-[size,12,20] outline-4 outline-yellow-400">
                    <AvatarImage src={getImageUrl(rank1?.user_profile.profile_picture || "")} />
                    <AvatarFallback className="bg-gradient-yellow">
                      <User2 className="size-6 text-white md:size-8" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute bottom-0 left-1/2 min-w-6 -translate-x-1/2 translate-y-1/2 rounded-xl bg-yellow-400 px-1 text-center font-medium text-yellow-700">
                    {rank1?.level}
                  </div>
                </div>

                <div className="flex flex-col items-center gap-6">
                  <div className="flex flex-col items-center gap-3">
                    <div>
                      <h2 className="clamp-[text,base,lg] line-clamp-1 max-w-[10ch] text-center leading-none font-semibold">
                        {rank1?.user_profile.username === session?.user.username ? (
                          <span className="bg-gradient-purple bg-clip-text text-transparent">
                            You
                          </span>
                        ) : (
                          rank1?.user_profile.full_name
                        )}
                      </h2>
                      <p className="text-muted-foreground text-center text-xs md:text-sm">
                        @{rank3?.user_profile.username}
                      </p>
                    </div>
                    <Badge className="bg-gradient-yellow">{parseNum(rank1?.xp)} XP</Badge>
                  </div>
                  <div className="clamp-[text,2xl,5xl] bg-gradient-yellow bg-clip-text font-bold text-transparent">
                    1
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Rank 3 */}
          <div className="relative row-span-3 mt-7.5 overflow-hidden rounded-xl bg-amber-200 p-px shadow-xl">
            <Spotlight className="from-primary to-primary bg-radial via-amber-800 blur-3xl" />

            <div className="relative h-full w-full rounded-xl bg-gradient-to-br from-orange-100 to-orange-200 py-6">
              <div className="flex flex-col items-center justify-center gap-4 px-6">
                <div className="relative size-fit">
                  <Avatar className="clamp-[size,12,20] outline-4 outline-orange-400">
                    <AvatarImage src={getImageUrl(rank3?.user_profile.profile_picture || "")} />
                    <AvatarFallback className="bg-gradient-orange">
                      <User2 className="size-6 text-white md:size-8" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute bottom-0 left-1/2 min-w-6 -translate-x-1/2 translate-y-1/2 rounded-xl bg-orange-400 px-1 text-center font-medium text-orange-700">
                    {rank3?.level}
                  </div>
                </div>

                <div className="flex flex-col items-center gap-6">
                  <div className="flex flex-col items-center gap-3">
                    <div>
                      <h2 className="clamp-[text,base,lg] line-clamp-1 max-w-[10ch] text-center leading-none font-semibold">
                        {rank3?.user_profile.username === session?.user.username ? (
                          <span className="bg-gradient-purple bg-clip-text text-transparent">
                            You
                          </span>
                        ) : (
                          rank3?.user_profile.full_name
                        )}
                      </h2>
                      <p className="text-muted-foreground text-xs leading-none md:text-sm">
                        @{rank3?.user_profile.username}
                      </p>
                    </div>
                    <Badge className="bg-gradient-orange">{parseNum(rank3?.xp)} XP</Badge>
                  </div>
                  <div className="clamp-[text,2xl,5xl] bg-gradient-orange bg-clip-text font-bold text-transparent">
                    3
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="sm:p-6 sm:pt-0">{data.length > 0 && <CardLeaderboard data={data} />}</div>
    </div>
  );
}
