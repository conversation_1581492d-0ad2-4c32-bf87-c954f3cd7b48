import { Suspense } from "react";
import { Ban, Calendar } from "lucide-react";
import { RiCalendar2Fill, RiCalendarFill, RiStarFill } from "@remixicon/react";

import { CardMission } from "../components/card-mission";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";

import { apiServer } from "@/lib/api-server";
import { tryCatch } from "@/utils/try-catch";
import { GamificationUserMissions } from "@/types/response.type";

export function GamificationMissionDetailView() {
  return (
    <div className="size-full flex-1">
      <Tabs defaultValue="daily">
        <div className="container mx-auto size-full flex-1 px-4 md:px-6">
          <TabsList className="bg-background mb-3 h-fit w-full overflow-x-auto border">
            <TabsTrigger
              className="data-[state=active]:bg-muted-foreground/20 data-[state=active]:text-foreground h-fit flex-col data-[state=active]:shadow-none md:flex-row"
              value="daily"
            >
              <RiCalendarFill />
              Harian
            </TabsTrigger>
            <TabsTrigger
              className="data-[state=active]:bg-muted-foreground/20 data-[state=active]:text-foreground h-fit flex-col data-[state=active]:shadow-none md:flex-row"
              value="weekly"
            >
              <RiCalendar2Fill />
              Mingguan
            </TabsTrigger>
            <TabsTrigger
              className="data-[state=active]:bg-muted-foreground/20 data-[state=active]:text-foreground h-fit flex-col data-[state=active]:shadow-none md:flex-row"
              value="monthly"
            >
              <Calendar />
              Bulanan
            </TabsTrigger>
            <TabsTrigger
              className="h-fit flex-col data-[state=active]:bg-yellow-100 data-[state=active]:text-yellow-700 data-[state=active]:shadow-none md:flex-row"
              value="special"
            >
              <RiStarFill />
              Spesial
            </TabsTrigger>
          </TabsList>
        </div>

        <div className="container mx-auto size-full flex-1">
          <Suspense
            fallback={
              <div className="space-y-3 px-4 md:px-6">
                {Array.from({ length: 3 }).map((_, index) => (
                  <Skeleton key={index} className="bg-background h-[5.625rem]" />
                ))}
              </div>
            }
          >
            <DailyMission />
          </Suspense>
        </div>
      </Tabs>
    </div>
  );
}

async function DailyMission() {
  const [res, err] = await tryCatch(
    apiServer({
      method: "GET",
      url: "/api/gamification/missions-user",
      cache: "no-store",
    }),
  );

  if (err || !res.ok) {
    return (
      <div className="text-muted-foreground flex size-full flex-1 flex-col items-center justify-center gap-4">
        <Ban className="clamp-[size,6,12]" />
        <p className="text-sm">Tidak ada misi yang tersedia</p>
      </div>
    );
  }

  const data = (await res.json()) as Array<GamificationUserMissions>;

  const dailyMission = data.filter((item) => item.mission.mission_type === "DAILY");
  const weeklyMission = data.filter((item) => item.mission.mission_type === "WEEKLY");
  const monthlyMission = data.filter((item) => item.mission.mission_type === "MONTHLY");
  const specialMission = data.filter((item) =>
    ["SPECIAL", "GOAL"].includes(item.mission.mission_type),
  );

  return (
    <div className="size-full flex-1">
      <TabsContent value="daily" className="px-4 md:px-6">
        <ul className="space-y-3">
          {dailyMission.length > 0 ? (
            dailyMission.map((item, idx) => (
              <CardMission
                as="li"
                key={idx}
                data={item}
                showProgress
                className="outline-2 outline-orange-50"
              />
            ))
          ) : (
            <div className="text-muted-foreground bg-card flex size-full flex-1 flex-col items-center justify-center gap-4 rounded-xl border py-6">
              <Ban className="clamp-[size,6,12]" />
              <p className="text-sm">Tidak ada misi Harian tersedia</p>
            </div>
          )}
        </ul>
      </TabsContent>

      <TabsContent value="weekly" className="px-4 md:px-6">
        <ul className="space-y-3">
          {weeklyMission.length > 0 ? (
            weeklyMission.map((item, idx) => (
              <CardMission
                as="li"
                key={idx}
                data={item}
                variant="weekly"
                showProgress
                className="outline-2 outline-blue-50"
              />
            ))
          ) : (
            <div className="text-muted-foreground bg-card flex size-full flex-1 flex-col items-center justify-center gap-4 rounded-xl border py-6">
              <Ban className="clamp-[size,6,12]" />
              <p className="text-sm">Tidak ada misi Mingguan tersedia</p>
            </div>
          )}
        </ul>
      </TabsContent>

      <TabsContent value="monthly" className="px-4 md:px-6">
        <ul className="space-y-3">
          {monthlyMission.length > 0 ? (
            monthlyMission.map((item, idx) => (
              <CardMission
                as="li"
                key={idx}
                data={item}
                showProgress
                className="outline-2 outline-blue-50"
              />
            ))
          ) : (
            <div className="text-muted-foreground bg-card flex size-full flex-1 flex-col items-center justify-center gap-4 rounded-xl border py-6">
              <Ban className="clamp-[size,6,12]" />
              <p className="text-sm">Tidak ada misi Bulanan tersedia</p>
            </div>
          )}
        </ul>
      </TabsContent>

      <TabsContent value="special" className="px-4 md:px-6">
        <ul className="space-y-3">
          {specialMission.length > 0 ? (
            specialMission.map((item, idx) => (
              <CardMission
                as="li"
                key={idx}
                data={item}
                variant={item.mission.mission_type === "SPECIAL" ? "special" : "goal"}
                showProgress
                className="outline-2 outline-yellow-50"
              />
            ))
          ) : (
            <div className="text-muted-foreground bg-card flex size-full flex-1 flex-col items-center justify-center gap-4 rounded-xl border py-6">
              <Ban className="clamp-[size,6,12]" />
              <p className="text-sm">Tidak ada misi Spesial tersedia</p>
            </div>
          )}
        </ul>
      </TabsContent>
    </div>
  );
}
