import Link from "next/link";
import { Suspense } from "react";
import { RiErrorWarningFill, RiTrophyFill } from "@remixicon/react";

import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

import { apiServer } from "@/lib/api-server";
import { auth } from "@/auth";
import { GamificationLeaderboard } from "@/types/response.type";
import { parseNum } from "@/utils/helper";
import { tryCatch } from "@/utils/try-catch";

export function GamificationLeaderboardView() {
  return (
    <Card className="rounded-none border-x-0 border-t-0 shadow-none sm:rounded-xl sm:border-x sm:border-t">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="text-primary flex items-center gap-2">
            <RiTrophyFill className="size-4" />
            <h3 className="clamp-[text,sm,base] leading-none font-bold">Papan <PERSON></h3>
          </div>

          <Link
            href="/dashboard/gamification/leaderboard"
            className="text-primary hover:text-primary/80 h-fit text-xs font-medium transition-opacity"
          >
            Lihat Semua
          </Link>
        </div>
      </CardHeader>

      <Suspense fallback={<Loading />}>
        <Leaderboard />
      </Suspense>
    </Card>
  );
}

async function Leaderboard() {
  const session = await auth();
  const [res, err] = await tryCatch(
    apiServer({
      method: "GET",
      url: "/api/gamification/leaderboard",
      cache: "no-store",
    }),
  );

  if (err || !res.ok) {
    return (
      <CardContent className="flex h-24 items-center justify-center">
        <div className="flex flex-col items-center justify-center">
          <RiErrorWarningFill className="text-muted-foreground size-10" />

          <p className="text-muted-foreground text-lg font-semibold">
            Tidak ada papan peringkat tersedia!
          </p>
        </div>
      </CardContent>
    );
  }

  const data = (await res.json()) as Array<GamificationLeaderboard>;

  const totalUsers = data.length;
  const rank1 = data.shift();
  const rank2 = data.shift();
  const rank3 = data.shift();

  const checkIsTop3 = () => {
    const topUsername = [
      rank1?.user_profile.username,
      rank2?.user_profile.username,
      rank3?.user_profile.username,
    ];

    if (topUsername.includes(session?.user.username)) return true;
    return false;
  };

  const restOfLeaderboard = data.find((item, idx) => {
    const checkTop3 = checkIsTop3();

    if (checkTop3) return idx === 0;

    return item.user_profile.username === session?.user.username;
  });

  return (
    <CardContent>
      <ul className="grid grid-cols-1 gap-6">
        {/* Rank 1 */}
        <li className="flex items-center gap-4 rounded-md border border-yellow-200 bg-yellow-50 px-4 py-3">
          <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-yellow-400 leading-none text-white md:size-8">
            1
          </div>

          <div className="w-full">
            <div className="mb-1 flex w-full justify-between gap-4">
              <div className="flex items-center gap-2">
                {rank1?.user_profile.username === session?.user.username && (
                  <span className="rounded-full border border-yellow-300 bg-yellow-100 px-2 py-0.5 text-xs font-medium text-yellow-500">
                    You
                  </span>
                )}
                <p className="clamp-[text,sm,base] line-clamp-1 max-w-[30ch] font-bold">
                  {rank1?.user_profile.full_name || "Unknown"}
                </p>
              </div>
              <p className="text-xs font-semibold text-yellow-500">Level {rank1?.level || 0}</p>
            </div>

            <p className="text-muted-foreground text-sm">
              <span className="font-medium">{parseNum(rank1?.xp)}</span>{" "}
              <span className="text-xs">XP</span> - {rank1?.rank}
            </p>
          </div>
        </li>

        {/* Rank 2 */}
        <li className="flex items-center gap-4 rounded-md border border-gray-100 bg-gray-50 px-4 py-3">
          <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-gray-400 leading-none text-white md:size-8">
            2
          </div>

          <div className="w-full">
            <div className="mb-1 flex w-full justify-between gap-4">
              <div className="flex items-center gap-2">
                {rank2?.user_profile.username === session?.user.username && (
                  <span className="rounded-full border border-gray-200 bg-gray-100 px-2 py-0.5 text-xs font-medium text-gray-500">
                    You
                  </span>
                )}
                <p className="clamp-[text,sm,base] line-clamp-1 max-w-[30ch] font-bold">
                  {rank2?.user_profile.username || "Unknown"}
                </p>
              </div>
              <p className="text-xs font-semibold text-gray-500">Level {rank2?.level || 0}</p>
            </div>
            <p className="text-muted-foreground text-sm">
              <span className="font-medium">{parseNum(rank2?.xp)}</span>{" "}
              <span className="text-xs">XP</span> - {rank2?.rank}
            </p>
          </div>
        </li>

        {/* Rank 3 */}
        <li className="flex items-center gap-4 rounded-md border border-amber-600/20 bg-amber-600/10 px-4 py-3">
          <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-amber-600 leading-none text-white md:size-8">
            3
          </div>

          <div className="w-full">
            <div className="mb-1 flex w-full justify-between gap-4">
              <div className="flex items-center gap-2">
                {rank3?.user_profile.username === session?.user.username && (
                  <span className="rounded-full border border-amber-600/20 bg-amber-600/10 px-2 py-0.5 text-xs font-medium text-amber-600">
                    You
                  </span>
                )}
                <p className="clamp-[text,sm,base] line-clamp-1 max-w-[30ch] font-bold">
                  {rank3?.user_profile.full_name || "Unknown"}
                </p>
              </div>
              <p className="text-xs font-semibold text-amber-600">Level {rank3?.level || 0}</p>
            </div>
            <p className="text-muted-foreground text-sm">
              <span className="font-medium">{parseNum(rank3?.xp)}</span>{" "}
              <span className="text-xs">XP</span> - {rank3?.rank}
            </p>
          </div>
        </li>

        {restOfLeaderboard && (
          <li className="flex items-center gap-4 rounded-md border border-blue-600/20 bg-blue-600/10 px-4 py-3">
            <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-blue-600 leading-none text-white md:size-8">
              {restOfLeaderboard?.leaderboard_rank}
            </div>

            <div className="w-full">
              <div className="mb-1 flex w-full justify-between gap-4">
                <div className="flex items-center gap-2">
                  {restOfLeaderboard?.user_profile.username === session?.user.username && (
                    <span className="rounded-full border border-blue-600/20 bg-blue-600/10 px-2 py-0.5 text-xs font-medium text-blue-600">
                      You
                    </span>
                  )}
                  <p className="clamp-[text,sm,base] line-clamp-1 max-w-[30ch] font-bold">
                    {restOfLeaderboard?.user_profile.full_name || "Unknown"}
                  </p>
                </div>
                <p className="text-xs font-semibold text-blue-600">
                  Level {restOfLeaderboard?.level || 0}
                </p>
              </div>
              <p className="text-muted-foreground text-sm">
                <span className="font-medium">{parseNum(restOfLeaderboard?.xp)}</span>{" "}
                <span className="text-xs">XP</span> - {restOfLeaderboard?.rank}
              </p>
            </div>
          </li>
        )}
      </ul>

      <div className="mt-4">
        <p className="text-muted-foreground text-right text-xs">Total {totalUsers} orang</p>
      </div>
    </CardContent>
  );
}

function Loading() {
  return (
    <CardContent className="grid grid-cols-1 gap-6">
      {Array.from({ length: 4 }).map((_, index) => (
        <Skeleton key={index} className="h-16" />
      ))}
    </CardContent>
  );
}
