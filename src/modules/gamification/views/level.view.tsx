import { Suspense } from "react";
import { FaDragon } from "react-icons/fa";
import { RiErrorWarningFill } from "@remixicon/react";
import {
  CalendarCheck,
  Crown,
  HelpCircle,
  Pencil,
  Shield,
  Sprout,
  UserCircleIcon,
} from "lucide-react";

import { LoadingAnimation } from "@/components/shared/loading-animation";
import { WrapperOpacity } from "@/components/shared/wrapper-opacity";
import { XpProgress } from "@/components/shared/xp-progress";
import { Card, CardContent } from "@/components/ui/card";

import { apiServer } from "@/lib/api-server";
import { parseNum } from "@/utils/helper";
import { tryCatch } from "@/utils/try-catch";
import { cn } from "@/utils/cn";

import { GamificationProfile, GamificationRank } from "@/types/response.type";
import { Progress } from "@/components/ui/progress";

const rankIcons: Record<GamificationRank, React.ReactNode> = {
  Beginner: <Sprout className="size-4" />,
  Master: <HelpCircle className="size-4" />,
  Grandmaster: <Shield className="size-4" />,
  Epic: <Pencil className="size-4" />,
  Legend: <Crown className="size-4" />,
  Mythic: <FaDragon className="size-4" />,
};

const variants: Record<GamificationRank, string> = {
  Beginner: "bg-green-500/20 text-green-500 border-green-500/30",
  Master: "bg-blue-500/20 text-blue-500 border-blue-500/30",
  Grandmaster: "bg-primary/20 text-primary border-primary/30",
  Epic: "bg-orange-500/20 text-orange-500 border-orange-500/30",
  Legend: "bg-amber-500/20 text-amber-500 border-amber-500/30",
  Mythic: "bg-yellow-500/20 text-yellow-500 border-yellow-500/30",
};

export function GamificationLevelView() {
  return (
    <div className="size-full flex-1">
      <Suspense fallback={<Loading />}>
        <Level />
      </Suspense>
    </div>
  );
}

async function Level() {
  const [res, err] = await tryCatch(
    apiServer({
      method: "GET",
      url: "/api/gamification/status",
      next: { revalidate: 60 * 15, tags: ["gamification-status"] },
      cache: "default",
    }),
  );

  if (err || !res.ok) {
    return (
      <CardContent className="flex h-24 items-center justify-center">
        <div className="flex flex-col items-center justify-center">
          <RiErrorWarningFill className="text-muted-foreground size-10" />

          <p className="text-muted-foreground text-lg font-semibold">Tidak ada level tersedia!</p>
        </div>
      </CardContent>
    );
  }

  const data = (await res.json()) as GamificationProfile;

  const countXP = Math.abs(data.level * 100);

  return (
    <WrapperOpacity className="flex flex-1 flex-col gap-6 px-4 pb-6 md:flex-row-reverse md:px-6">
      <Card className="top-[calc(var(--header-height)+1.5rem)] h-fit shadow-none md:sticky">
        <CardContent>
          <div className="flex flex-col items-center">
            <div className="mb-4 flex items-center gap-4">
              <h2 className="font-medium">
                Kamu sekarang <span className="font-bold">Level {data.level}</span>
              </h2>
              <Rank rank={data.rank} />
            </div>

            <XpProgress level={data.level} xp={data.xp} className="mb-2 w-full" />

            <p className="text-muted-foreground text-xs md:text-sm">
              Kumpulkan{" "}
              <span className="text-[1.1em] font-semibold">{parseNum(countXP - data.xp)}</span> XP
              lagi untuk naik ke Level {data.level + 1}.
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="flex flex-1 flex-col-reverse gap-6 md:flex-col">
        <Card className="shadow-none">
          <CardContent>
            <Tips />
          </CardContent>
        </Card>

        <Ranks level={data.level} xp={data.xp} rank={data.rank} />
      </div>
    </WrapperOpacity>
  );
}

function Ranks({ level, xp, rank }: { level: number; xp: number; rank: GamificationRank }) {
  const ranks = [
    {
      rank: "Beginner",
      description: "Baru memulai perjalanan, belajar dasar interview & CV.",
      min: 1,
      max: 10,
    },
    {
      rank: "Master",
      description: "Menguasai dasar interview & CV, mulai latihan intensif.",
      min: 11,
      max: 20,
    },
    {
      rank: "Grandmaster",
      description: "Tangguh, punya skor stabil & pencapaian besar.",
      min: 21,
      max: 30,
    },
    {
      rank: "Epic",
      description: `Mulai jadi "Pro-Candidate" dalam interview.`,
      min: 31,
      max: 40,
    },
    {
      rank: "Legend",
      description: "Jadi panutan, dikenal di leaderboard.",
      min: 41,
      max: 50,
    },
    {
      rank: "Mythic",
      description: "Rank tertinggi, eksklusif, hampir mustahil dicapai.",
      min: 51,
      max: null,
    },
  ];

  return (
    <ul className="space-y-6">
      {ranks.map((item, idx) => {
        const iconVariant: Record<GamificationRank, React.ReactNode> = {
          Beginner: "bg-rank-beginner border-green-500/30",
          Master: "bg-rank-master border-blue-500/30",
          Grandmaster: "bg-rank-grandmaster border-purple-500/30",
          Epic: "bg-rank-epic border-orange-500/30",
          Legend: "bg-rank-legend border-yellow-500/30",
          Mythic: "bg-rank-mythic border-red-500/30 text-[#CA8A04]",
        };

        const progressVariant: Record<GamificationRank, string> = {
          Beginner: "bg-muted [&_[data-slot=progress-indicator]]:bg-green-500",
          Master: "bg-muted [&_[data-slot=progress-indicator]]:bg-blue-500",
          Grandmaster: "bg-muted [&_[data-slot=progress-indicator]]:bg-purple-500",
          Epic: "bg-muted [&_[data-slot=progress-indicator]]:bg-yellow-500",
          Legend: "bg-muted [&_[data-slot=progress-indicator]]:bg-orange-500",
          Mythic: "bg-muted [&_[data-slot=progress-indicator]]:bg-[#FACC15]",
        };

        const isComplete = item.min <= level;

        const countProgressValue = () => {
          const minXp = (item.min - 1) * 100;
          const maxXp = item.max ? item.max * 100 : Infinity;
          return xp >= maxXp
            ? 100
            : Math.max(0, Math.min(100, ((xp - minXp) / (maxXp - minXp)) * 100));
        };

        let message;

        if (level >= ranks[idx].max!) {
          message = "Pencapaian telah diraih!";
        } else if (isComplete && item.max) {
          message = `Level ${level} dari ${item.max}`;
        } else {
          message = `Terkunci - Capai Level ${item.max ?? 51}`;
        }

        return (
          <li
            key={idx}
            className={cn(
              item.rank === rank && iconVariant[item.rank as GamificationRank],
              "bg-card rounded-xl border p-6 opacity-50",
              isComplete && "opacity-100",
            )}
          >
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div className="flex items-center gap-4">
                <div
                  className={cn(
                    "clamp-[size,14,16] [&>svg]:clamp-[size,4,6] flex shrink-0 items-center justify-center rounded-md text-white shadow-md",
                    iconVariant[item.rank as GamificationRank],
                  )}
                >
                  {rankIcons[item.rank as GamificationRank]}
                </div>

                <div>
                  <h5 className="clamp-[text,base,xl] font-bold">
                    {item.rank} (Level {item.min}
                    {item.max ? ` - ${item.max}` : "+"})
                  </h5>
                  <p className="text-muted-foreground clamp-[text,sm,base]">{item.description}</p>
                </div>
              </div>

              <div className="w-full space-y-1 md:max-w-sm md:self-start">
                <Progress
                  value={level <= 50 ? countProgressValue() : 100}
                  className={progressVariant[item.rank as GamificationRank]}
                />

                <p className="text-muted-foreground text-xs md:text-end">{message}</p>
              </div>
            </div>
          </li>
        );
      })}
    </ul>
  );
}

function Rank({ rank, className }: { className?: string; rank: GamificationRank }) {
  return (
    <span
      className={cn(
        "flex items-center gap-2 rounded-md border px-2 py-0.5",
        variants[rank],
        className,
      )}
    >
      {rankIcons[rank]}
      <span className="text-sm font-medium">{rank}</span>
    </span>
  );
}

function Tips() {
  const tips = [
    {
      icon: <CalendarCheck className="text-primary size-4" />,
      title: "Konsisten Berlatih",
      description: "Lakukan latihan interview rutin setiap hari",
    },
    {
      icon: <HelpCircle className="size-4 text-blue-500" />,
      title: "Capai Target Skor",
      description: "Tingkatkan skor interview secara bertahap",
    },
    {
      icon: <UserCircleIcon className="text-primary size-4" />,
      title: "Ikuti Kompetisi",
      description: "Berpartisipasi dalam leaderboard",
    },
  ];

  return (
    <div className="flex flex-col items-center justify-center gap-4">
      <div className="mb-4 text-center">
        <h4 className="clamp-[text,base,lg] font-bold">Tips untuk Naik Level</h4>
        <p className="text-muted-foreground text-xs md:text-sm">
          berikut adalah tips untuk naik level.
        </p>
      </div>

      <ul className="grid w-full grid-cols-1 gap-6 @lg/dashboard:grid-cols-2 @3xl/dashboard:grid-cols-3">
        {tips.map((item, index) => {
          return (
            <li
              key={index}
              className="bg-muted flex flex-col items-center justify-center rounded-md border px-3 py-3 text-center md:px-4"
            >
              {item.icon}
              <h5 className="clamp-[text,base,lg] font-bold">{item.title}</h5>
              <p className="text-muted-foreground text-xs text-balance md:text-sm">
                {item.description}
              </p>
            </li>
          );
        })}
      </ul>
    </div>
  );
}

function Loading() {
  return (
    <div className="flex size-full flex-1 items-center justify-center">
      <LoadingAnimation />
    </div>
  );
}
