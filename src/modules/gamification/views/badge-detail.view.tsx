import { HelpCircle } from "lucide-react";
import { Suspense } from "react";

import { BadgeProgress } from "@/components/shared/badge-progress";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { apiServer } from "@/lib/api-server";
import { tryCatch } from "@/utils/try-catch";

import { GamificationBadge } from "@/types/response.type";
import { LoadingAnimation } from "@/components/shared/loading-animation";

export function GamificationBadgeDetailView() {
  return (
    <div className="flex size-full flex-1 flex-col gap-6 py-6">
      <Suspense fallback={<LoadingBadgeDetail />}>
        <BadgeDetail />
      </Suspense>
      <Suspense fallback={<LoadingBadges />}>
        <Badges />
      </Suspense>
    </div>
  );
}

async function BadgeDetail() {
  const [res, err] = await tryCatch(
    apiServer({
      method: "GET",
      url: "/api/gamification/badges-user",
      cache: "default",
      next: { revalidate: 60 * 15, tags: ["gamification-badge"] },
    }),
  );

  if (err || !res.ok) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Card className="w-full max-w-md border-0 shadow-lg">
          <CardHeader className="text-center">
            <div className="bg-muted mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full">
              <HelpCircle className="text-muted-foreground h-8 w-8" />
            </div>
            <CardTitle className="text-xl font-semibold">Koleksi Badge</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-muted-foreground text-sm">Tidak ada badge tersedia saat ini</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const data = (await res.json()) as Array<GamificationBadge>;
  const countEarnedBadge = data.filter((item) => item.earned_at).length;

  return (
    <div className="px-4 md:px-6">
      <Card className="bg-gradient-purple w-full text-white shadow-none md:h-[120px]">
        <CardContent>
          <div className="flex flex-col gap-6 md:flex-row md:items-center md:justify-between">
            <div className="space-y-1">
              <h1 className="clamp-[text,lg,3xl] font-bold">Koleksi Badge Kamu</h1>
              <p className="clamp-[text,xs,base] text-white/75">
                Kumpulkan semua badge untuk membuka pencapaian istimewa!
              </p>
            </div>

            <div className="space-y-1 self-end text-end">
              <div className="clamp-[text,2xl,4xl] font-bold">
                {countEarnedBadge}/{data.length}
              </div>
              <p className="clamp-[text,xs,base] text-white/75">Badge Terkumpul</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

async function Badges() {
  const [res, err] = await tryCatch(
    apiServer({
      method: "GET",
      url: "/api/gamification/badges-user",
      cache: "default",
      next: { revalidate: 60 * 15, tags: ["gamification-badge"] },
    }),
  );

  if (err || !res.ok) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Card className="w-full max-w-md border-0 shadow-lg">
          <CardHeader className="text-center">
            <div className="bg-muted mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full">
              <HelpCircle className="text-muted-foreground h-8 w-8" />
            </div>
            <CardTitle className="text-xl font-semibold">Koleksi Badge</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-muted-foreground text-sm">Tidak ada badge tersedia saat ini</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const data = (await res.json()) as Array<GamificationBadge>;

  return (
    <div className="px-4 md:px-6">
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6">
        {data.map((item, idx) => {
          return <BadgeProgress key={idx} as="div" data={item} />;
        })}
      </div>
    </div>
  );
}

function LoadingBadgeDetail() {
  return (
    <div className="px-4 md:px-6">
      <Card className="bg-gradient-purple h-[120px] animate-pulse text-white shadow-none"></Card>
    </div>
  );
}

function LoadingBadges() {
  return (
    <div className="size-full flex-1 items-center justify-center">
      <LoadingAnimation />
    </div>
  );
}
