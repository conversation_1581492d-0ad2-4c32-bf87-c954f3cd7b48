import Link from "next/link";
import { Suspense } from "react";
import { RiErrorWarningFill, RiStarFill } from "@remixicon/react";
import { format } from "date-fns";
import { Calendar, CalendarHeart, User2 } from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import { Badge, badgeVariants } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { WrapperOpacity } from "@/components/shared/wrapper-opacity";
import { LoadingRipple } from "@/components/shared/loading-ripple";
import { XpProgress } from "@/components/shared/xp-progress";

import { apiServer } from "@/lib/api-server";
import { tryCatch } from "@/utils/try-catch";
import { GamificationProfile, GamificationRank } from "@/types/response.type";
import { getImageUrl } from "@/utils/image-url";
import { parseNum } from "@/utils/helper";
import { VariantProps } from "class-variance-authority";

export function GamificationProfileView() {
  return (
    <Card className="col-span-full rounded-none border-x-0 border-t-0 shadow-none">
      <Suspense fallback={<Loading />}>
        <Profile />
      </Suspense>
    </Card>
  );
}

async function Profile() {
  const [res, err] = await tryCatch(
    apiServer({
      method: "GET",
      url: "/api/gamification/status",
      next: { revalidate: 60 * 15, tags: ["gamification-status"] },
      cache: "default",
    }),
  );

  if (err || !res.ok) {
    return (
      <CardContent className="flex h-24 items-center justify-center">
        <WrapperOpacity className="flex flex-col items-center justify-center">
          <RiErrorWarningFill className="text-muted-foreground size-10" />

          <p className="text-muted-foreground text-lg font-semibold">
            Tidak ada papan peringkat tersedia!
          </p>
        </WrapperOpacity>
      </CardContent>
    );
  }

  const data = (await res.json()) as GamificationProfile;

  const badgeVariant: Record<GamificationRank, VariantProps<typeof badgeVariants>["variant"]> = {
    Beginner: "secondary",
    Master: "secondary",
    Grandmaster: "black",
    Epic: "black",
    Legend: "pending",
    Mythic: "destructive",
  };

  return (
    <CardContent className="container mx-auto">
      <WrapperOpacity
        transition={{ duration: 0.5 }}
        className="flex h-[350px] w-full flex-col justify-between gap-10 md:h-[125px] md:flex-row md:items-center md:gap-4"
      >
        <div className="flex flex-col gap-4 md:flex-row md:items-center">
          <Avatar className="clamp-[size,16,28] shrink-0">
            <AvatarImage src={getImageUrl(data.user_profile.profile_picture || "")} />
            <AvatarFallback className="bg-gradient-purple">
              <User2 className="size-6 text-white md:size-8" />
            </AvatarFallback>
          </Avatar>

          <div>
            <h1 className="clamp-[text,lg,2xl] mb-0.5 line-clamp-1 max-w-[30ch] leading-none font-bold">
              {data.user_profile.full_name}
            </h1>
            <p className="text-muted-foreground mb-3 text-xs leading-none md:text-sm">
              @{data.user_profile.username}
            </p>

            <p className="text-muted-foreground mb-3 line-clamp-2 max-w-[60ch] text-xs">
              {data.user_profile.bio || "Tidak ada bio"}
            </p>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <CalendarHeart className="text-muted-foreground size-4" />
                <p className="text-muted-foreground text-xs">
                  Born {format(new Date(data.user_profile.date_of_birth), "MMM, d yyyy")}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="text-muted-foreground size-4" />
                <p className="text-muted-foreground text-xs">
                  Joined {format(new Date(data.user_profile.created_at), "MMM yyyy")}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex w-full flex-col justify-between gap-4 md:max-w-md">
          <div className="w-full">
            <Badge
              variant={badgeVariant[data.rank]}
              className="clamp-[text,xs,base] mb-2 font-semibold md:self-end"
            >
              Level {data.level}
            </Badge>

            <XpProgress level={data.level} xp={data.xp} className="mb-1" />

            <div className="flex justify-between">
              <p className="text-xs font-medium">Rank: {data.rank}</p>
              <p className="text-muted-foreground text-right text-xs uppercase">
                {parseNum(data.xp)} XP
              </p>
            </div>
          </div>

          <Button asChild className="bg-gradient-red w-full hover:opacity-90 md:w-fit md:self-end">
            <Link href="/dashboard/gamification/level">
              <RiStarFill />
              Lihat Level & Rank
            </Link>
          </Button>
        </div>
      </WrapperOpacity>
    </CardContent>
  );
}

function Loading() {
  return (
    <CardContent>
      <div className="flex h-[350px] items-center justify-center md:h-[125px]">
        <LoadingRipple />
      </div>
    </CardContent>
  );
}
