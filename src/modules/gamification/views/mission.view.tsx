import Link from "next/link";
import { Suspense } from "react";
import { Check, List } from "lucide-react";
import { RiCalendarEventFill, RiCalendarFill, RiErrorWarningFill } from "@remixicon/react";

import { Progress } from "@/components/ui/progress";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

import { apiServer } from "@/lib/api-server";
import { cn } from "@/utils/cn";
import { tryCatch } from "@/utils/try-catch";
import { GamificationUserMissions } from "@/types/response.type";
import { Skeleton } from "@/components/ui/skeleton";
import { parseNum } from "@/utils/helper";
import { ButtonReward } from "../components/button-reward";
import { CardMission } from "../components/card-mission";

export function GamificationMissionView({ className }: { className?: string }) {
  return (
    <Card
      className={cn(
        "col-span-full rounded-none border-x-0 border-t-0 sm:border-t shadow-none sm:rounded-xl sm:border-x",
        className,
      )}
    >
      <CardHeader className="border-b">
        <div className="flex justify-between">
          <div className="flex items-center gap-2 leading-none">
            <List className="size-4 md:size-6" />
            <h3 className="clamp-[text,sm,lg] font-bold">Misi & Aktivitas</h3>
          </div>

          <Link
            href="/dashboard/gamification/mission"
            className="text-primary hover:text-primary/80 clamp-[text,xs,sm] font-medium transition-opacity"
          >
            Lihat Semua
          </Link>
        </div>
      </CardHeader>

      <Suspense fallback={<Loading />}>
        <Mission />
      </Suspense>
    </Card>
  );
}

async function Mission() {
  const [res, err] = await tryCatch(
    apiServer({
      method: "GET",
      url: "/api/gamification/missions-user",
      cache: "no-store",
    }),
  );

  if (err || !res.ok) {
    return (
      <CardContent className="flex h-24 items-center justify-center">
        <div className="flex flex-col items-center justify-center">
          <RiErrorWarningFill className="text-muted-foreground size-10" />

          <p className="text-muted-foreground text-lg font-semibold">Tidak ada misi tersedia!</p>
        </div>
      </CardContent>
    );
  }

  const data = (await res.json()) as Array<GamificationUserMissions>;

  const dailyMission = data.filter((item) => item.mission.mission_type === "DAILY");
  const weeklyMission = data.filter((item) => item.mission.mission_type === "WEEKLY");

  return (
    <CardContent className="grid grid-cols-1 gap-6 @lg/dashboard:grid-cols-2">
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <RiCalendarEventFill className="clamp-[size,3,4] text-primary shrink-0" />
          <h4 className="clamp-[text,xs,sm] bg-gradient-purple bg-clip-text font-bold text-transparent">
            Misi Harian
          </h4>
        </div>

        {dailyMission.length > 0 ? (
          <ul className="space-y-4">
            {dailyMission.map((item, idx) => {
              return <CardMission as="li" key={idx} data={item} />;
            })}
          </ul>
        ) : (
          <p className="text-muted-foreground text-sm">Tidak ada misi harian tersedia</p>
        )}
      </div>

      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <RiCalendarFill className="clamp-[size,3,4] shrink-0 text-blue-400" />
          <h4 className="clamp-[text,xs,sm] bg-gradient-blue bg-clip-text font-bold text-transparent">
            Misi Mingguan
          </h4>
        </div>

        {weeklyMission.length > 0 ? (
          <ul className="space-y-4">
            {weeklyMission.map((item, idx) => {
              const isCompleted = item.is_completed;
              return <CardMission as="li" key={idx} data={item} variant="weekly" showProgress />;
            })}
          </ul>
        ) : (
          <p className="text-muted-foreground text-sm">Tidak ada misi mingguan tersedia</p>
        )}
      </div>
    </CardContent>
  );
}

function Loading() {
  return (
    <CardContent className="grid grid-cols-1 gap-6 @lg/dashboard:grid-cols-2">
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <RiCalendarEventFill className="clamp-[size,3,4] shrink-0" />
          <h4 className="clamp-[text,xs,sm] font-bold">Misi Harian</h4>
        </div>

        <ul className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <Skeleton key={index} className="h-24" />
          ))}
        </ul>
      </div>

      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <RiCalendarFill className="clamp-[size,3,4] shrink-0" />
          <h4 className="clamp-[text,xs,sm] font-bold">Misi Mingguan</h4>
        </div>

        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <Skeleton key={index} className="h-24" />
          ))}
        </div>
      </div>
    </CardContent>
  );
}
