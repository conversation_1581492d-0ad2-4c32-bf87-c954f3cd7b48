"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardAction, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { GamificationLeaderboard } from "@/types/response.type";
import { parseNum } from "@/utils/helper";
import { getImageUrl } from "@/utils/image-url";
import { User2 } from "lucide-react";
import { useSession } from "next-auth/react";
import { useState } from "react";

export function CardLeaderboard({ data }: { data: Array<GamificationLeaderboard> }) {
  // TODO: Pagination if data.length > 10

  const { data: session } = useSession();
  const [users, setUsers] = useState(data.slice(0, 10));

  const handleLoadMore = () => {
    setUsers(data.slice(0, users.length + 10));
  };

  const handleLoadLess = () => {
    setUsers(data.slice(0, users.length - 10));
  };

  return (
    <Card className="rounded-none border-0 shadow-lg md:rounded-xl">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Peringkat Lainnya</CardTitle>
        {users.length > 10 && (
          <CardAction>
            <Button
              onClick={handleLoadLess}
              variant="outline"
              className="text-primary text-sm hover:underline"
              disabled={users.length <= 10}
            >
              Tampilkan Lebih Sedikit
            </Button>
          </CardAction>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {data.slice(0, 7).map((participant, index) => (
            <div
              key={participant.user_profile.username}
              className="bg-muted/50 hover:bg-muted flex items-center justify-between gap-4 rounded-lg p-3 transition-colors"
            >
              <div className="flex items-center gap-3">
                <div className="bg-muted-foreground/10 flex size-8 shrink-0 items-center justify-center rounded-full text-sm font-medium">
                  {index + 4}
                </div>
                <Avatar className="size-8 shrink-0">
                  <AvatarImage src={getImageUrl(participant.user_profile.profile_picture || "")} />
                  <AvatarFallback className="text-xs">
                    <User2 className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="line-clamp-1 font-medium">
                    {participant.user_profile.username === session?.user.username
                      ? "You"
                      : participant.user_profile.full_name}
                  </div>
                  <p className="text-muted-foreground text-xs leading-none md:text-sm">
                    @{participant.user_profile.username}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Badge variant="outline">{parseNum(participant.xp)} XP</Badge>
                <Button size={"sm"} variant={"outline"} disabled>
                  Follow
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
