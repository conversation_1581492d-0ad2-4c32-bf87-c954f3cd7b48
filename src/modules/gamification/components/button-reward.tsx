"use client";

import { useActionState } from "react";
import { useRouter } from "next/navigation";

import { rewards } from "../services/rewards.service";

import { Button } from "@/components/ui/button";
import { parseNum } from "@/utils/helper";

export function ButtonReward({ xp }: { className?: string; xp: number }) {
  const router = useRouter();
  const [, action, pending] = useActionState(async () => {
    await rewards("login");

    router.refresh();
  }, undefined);

  return (
    <form>
      <Button
        size="sm"
        formAction={action}
        disabled={pending}
        className="bg-gradient-orange text-xs font-semibold hover:opacity-80"
      >
        Claim <span>+{parseNum(xp)} XP</span>
      </Button>
    </form>
  );
}
