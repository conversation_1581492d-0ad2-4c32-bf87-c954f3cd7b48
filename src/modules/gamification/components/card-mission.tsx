import { GamificationUserMissions } from "@/types/response.type";
import { cn } from "@/utils/cn";
import { ButtonReward } from "./button-reward";
import { Check } from "lucide-react";
import { Progress } from "@/components/ui/progress";

type CardMissionProps = {
  as: "li" | "div";
  showProgress?: boolean;
  data: GamificationUserMissions;
  className?: string;
  variant?: "daily" | "weekly" | "monthly" | "special" | "goal";
};

export function CardMission({
  as,
  data,
  className,
  showProgress = false,
  variant = "daily",
}: Readonly<CardMissionProps>) {
  const Slot = as === "li" ? "li" : "div";

  const isCompleted = data.is_completed;
  const dailyLogin = data.action_name === "login";

  return (
    <Slot
      className={cn(
        "bg-muted border-muted-foreground flex h-24 items-center gap-4 rounded-md border-l-4 px-4 py-3",
        className,
        {
          "border-orange-500 bg-orange-100": isCompleted && variant === "daily",
          "border-blue-500 bg-blue-100": isCompleted && variant === "weekly",
          "border-yellow-500 bg-yellow-100": isCompleted && variant === "special",
          "bg-gradient-blue border-blue-500": isCompleted && variant === "goal",
          "border-green-500 bg-green-100": isCompleted && variant === "monthly",
        },
      )}
    >
      <div
        className={cn(
          "bg-muted-foreground flex size-10 shrink-0 items-center justify-center rounded-full",
          {
            "bg-orange-500": isCompleted && variant === "daily",
            "bg-blue-500": isCompleted && variant === "weekly",
            "bg-yellow-500": isCompleted && variant === "special",
            "bg-green-500": isCompleted && variant === "monthly",
            "bg-secondary": isCompleted && variant === "goal",
          },
        )}
      >
        {isCompleted ? (
          <Check
            className={cn("text-background size-6", {
              "text-foreground": variant === "goal",
            })}
            strokeWidth={3}
          />
        ) : (
          <div className="bg-background size-1/2 rounded-full" />
        )}
      </div>

      <div className="w-full space-y-2">
        <div className="w-full space-y-0.5">
          <div className="flex w-full items-center justify-between gap-6 text-sm">
            <p
              className={cn("line-clamp-1 max-w-[40ch] font-medium @md/dashboard:max-w-[50ch]", {
                "text-white": variant === "goal",
              })}
            >
              {data.mission.name}
            </p>

            {dailyLogin && !isCompleted ? (
              <ButtonReward xp={data.mission.xp_reward} />
            ) : (
              <p
                className={cn("text-xs font-semibold", {
                  "text-white/80": variant === "goal",
                })}
              >
                +{data.mission.xp_reward} XP
              </p>
            )}
          </div>
          <p
            className={cn(
              "text-muted-foreground line-clamp-1 max-w-[60ch] text-xs",
              variant === "goal" && "text-white",
            )}
          >
            {data.mission.description}
          </p>
        </div>

        {showProgress && (
          <div className="relative space-y-1">
            <Progress
              value={(data.current_count / data.mission.completion_count) * 100}
              className={cn(
                variant === "daily" &&
                  "data-[slot=progress]:bg-orange-500/20 [&_[data-slot=progress-indicator]]:bg-orange-500",
                variant === "weekly" &&
                  "data-[slot=progress]:bg-blue-500/20 [&_[data-slot=progress-indicator]]:bg-blue-500",
                variant === "monthly" &&
                  "data-[slot=progress]:bg-green-500/20 [&_[data-slot=progress-indicator]]:bg-green-500",
                variant === "special" &&
                  "data-[slot=progress]:bg-yellow-500/20 [&_[data-slot=progress-indicator]]:bg-yellow-500",
                variant === "goal" &&
                  "data-[slot=progress]:bg-zinc-50/20 [&_[data-slot=progress-indicator]]:bg-white",
              )}
            />
            <p
              className={cn(
                "text-muted-foreground right-0 bottom-full z-10 -translate-y-0.5 text-end text-xs font-medium md:absolute",
                variant === "goal" && "text-white",
              )}
            >
              {data.current_count !== data.mission.completion_count
                ? `${data.current_count} / ${data.mission.completion_count} Selesai`
                : "Complete"}
            </p>
          </div>
        )}
      </div>
    </Slot>
  );
}
