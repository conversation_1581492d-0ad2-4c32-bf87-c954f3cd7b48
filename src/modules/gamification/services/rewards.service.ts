import { apiServer } from "@/lib/api-server";
import { GamificationAction } from "@/types/response.type";

export async function rewards(action_name: GamificationAction) {
  try {
    const res = await apiServer({
      method: "POST",
      url: "/api/gamification/award-xp/",
      body: JSON.stringify({ action_name }),
    });

    if (!res.ok) {
      return;
    }

    const response = await res.json();
    console.log("Reward response");
    console.log(response);
  } catch (error) {
    console.log(error);
  }
}
