import { easing } from "@/utils/animation";
import { cn } from "@/utils/cn";
import { motion } from "motion/react";

interface ChatMessageProps {
  message: {
    text: string;
    type: "ai" | "user";
    timestamp: Date;
  };
}

export function ChatMessage({ message }: ChatMessageProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{
        opacity: 1,
        y: 0,
        transition: { duration: 0.5, ease: easing.out },
      }}
      exit={{ opacity: 0, y: -20, transition: { duration: 0.3 } }}
      className={cn("flex flex-col gap-2.5", message.type === "user" ? "items-end" : "items-start")}
    >
      <p className="font-sans text-xs leading-none font-semibold md:font-medium">
        {message.type === "user" ? (
          <span className="text-white md:text-primary">You</span>
        ) : (
          <span className="text-white md:text-foreground">G-Brain AI</span>
        )}
      </p>
      <div
        className={cn(
          "max-w-[80%] rounded-lg md:px-4 md:py-2",
          message.type === "user"
            ? "md:bg-secondary md:text-secondary-foreground bg-transparent text-white"
            : "md:bg-gradient-purple bg-transparent text-white",
        )}
      >
        <p className="text-sm">{message.text || "No Text."}</p>
        <p
          className={cn(
            "mt-1 text-xs hidden md:block",
            message.type !== "user" ? "text-right" : "text-foreground text-left",
          )}
        >
          {message.timestamp.toLocaleTimeString("id-ID", {
            hour: "numeric",
            minute: "numeric",
          })}
        </p>
      </div>
    </motion.div>
  );
}
