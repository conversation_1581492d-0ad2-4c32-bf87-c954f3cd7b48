"use client";

import { useState } from "react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { IInterviewResult } from "@/types/response.type";

type QnaProps = {
  data: IInterviewResult;
};

export function InterviewSessionQna({ data }: QnaProps) {
  const { questions, answers } = data;

  const [currentPage, setCurrentPage] = useState(1);
  const questionsPerPage = 3;
  const totalPages = Math.ceil(questions.length / questionsPerPage);
  const startIndex = (currentPage - 1) * questionsPerPage;
  const paginatedQuestions = questions.slice(startIndex, startIndex + questionsPerPage);

  return (
    <Card className="shadow-none">
      <CardHeader className="border-b">
        <CardTitle>Interview Questions & Answers</CardTitle>
        <CardDescription>Detailed Q&A session from the interview</CardDescription>
      </CardHeader>

      <CardContent>
        <div className="space-y-6">
          {paginatedQuestions.map((question, index) => {
            const answer = answers.find((a) => a.question_id === question.id);
            return (
              <div key={question.id} className="border-l-4 border-blue-200 pl-4">
                <div className="mb-3">
                  <span className="mb-2 inline-block rounded bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                    Question {startIndex + index + 1}
                  </span>
                  <p className="font-medium text-gray-900">{question.question}</p>
                </div>
                {answer && (
                  <div className="bg-muted rounded-md p-3">
                    <p className="text-muted-foreground text-sm">{answer.answer}</p>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="mt-6 flex items-center justify-between border-t pt-4">
            <button
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="bg-background rounded border px-3 py-1 text-sm disabled:opacity-50"
            >
              Previous
            </button>
            <span className="text-sm text-gray-600">
              Halaman <strong>{currentPage}</strong> dari <strong>{totalPages}</strong>
            </span>
            <button
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="bg-background rounded border px-3 py-1 text-sm disabled:opacity-50"
            >
              Next
            </button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
