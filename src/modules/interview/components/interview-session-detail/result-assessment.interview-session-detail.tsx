import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";

import { IInterviewResult } from "@/types/response.type";
import { getScoreColor } from "../../utils";

type AssessmentProps = {
  data: IInterviewResult;
};

export function InterviewSessionResultAssessment({ data }: AssessmentProps) {
  const { result } = data;

  return (
    <Card className="shadow-none">
      <CardHeader>
        <CardTitle>Assessment Results</CardTitle>
        <CardDescription>Overall evaluation and recommendations</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <div className="mb-2 flex items-center justify-between">
            <span className="text-sm font-medium">Overall Score</span>
            <span className={`font-bold ${getScoreColor(result.final_score)}`}>
              {result.final_score}%
            </span>
          </div>
          <Progress value={result.final_score} className="h-2" />
        </div>

        <Separator />

        <div>
          <h4 className="mb-2 flex items-center font-semibold text-green-700">
            <CheckCircle className="mr-2 h-4 w-4" />
            Strengths
          </h4>
          <p className="text-sm text-gray-700">{result.strengths}</p>
        </div>

        <div>
          <h4 className="mb-2 flex items-center font-semibold text-orange-700">
            <AlertTriangle className="mr-2 h-4 w-4" />
            Areas for Improvement
          </h4>
          <p className="text-sm text-gray-700">{result.gaps}</p>
        </div>

        <div>
          <h4 className="mb-2 font-semibold text-blue-700">Recommendation</h4>
          <p className="text-sm text-gray-700">{result.recommendation}</p>
        </div>
      </CardContent>
    </Card>
  );
}
