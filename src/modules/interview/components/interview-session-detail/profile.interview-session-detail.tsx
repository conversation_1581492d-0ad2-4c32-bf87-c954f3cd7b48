import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { IInterviewResult } from "@/types/response.type";
import { cn } from "@/utils/cn";
import { getImageUrl } from "@/utils/image-url";
import { Award, GraduationCap, MapPin, User2Icon } from "lucide-react";

type InterviewSessionProfileProps = {
  className?: string;
  data: IInterviewResult;
};

export function InterviewSessionProfile({ data, className }: InterviewSessionProfileProps) {
  return (
    <Card className={cn("shadow-none", className)}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Avatar className="md:size-12">
            {data.user.profile_picture && (
              <AvatarImage src={getImageUrl(data.user.profile_picture)} />
            )}
            <AvatarFallback>
              {data.user.full_name ? (
                data.user.full_name
                  .split(" ")
                  .map((name) => name.charAt(0))
                  .join("")
              ) : (
                <User2Icon className="size-4 shrink-0 md:size-5" />
              )}
            </AvatarFallback>
          </Avatar>

          <div>
            <h3>{data.user.full_name}</h3>
            <p className="text-muted-foreground text-sm">{data.user?.email || "-"}</p>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="flex items-center space-x-2 text-sm leading-none">
          <MapPin className="size-4 shrink-0 text-gray-500" />
          <span>{data.interview.domisili_saat_ini}</span>
        </div>
        <div className="flex items-center space-x-2 text-sm leading-none">
          <GraduationCap className="size-4 shrink-0 text-gray-500" />
          <span>{data.interview.pendidikan}</span>
        </div>
        <div className="flex items-center space-x-2 text-sm leading-none">
          <Award className="size-4 shrink-0 text-gray-500" />
          <span>{data.interview.sertifikasi}</span>
        </div>

        <Separator />

        <div className="space-y-1">
          <div className="flex gap-2">
            <p className="text-muted-foreground text-sm font-medium">Experience</p>
            <p className="text-sm">{data.interview.years_of_experience} years</p>
          </div>
          <p className="text-sm">{data.interview.pengalaman_relevan}</p>
        </div>

        <div className="space-y-2">
          <p className="text-muted-foreground text-sm font-medium">Tools & Technologies</p>
          <div className="flex flex-wrap gap-1">
            {data.interview.tools.split(", ").map((tool, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {tool}
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
