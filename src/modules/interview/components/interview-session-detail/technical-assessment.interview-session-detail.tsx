import { MessageSquare } from "lucide-react";

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { IInterviewResult } from "@/types/response.type";

type TechnicalAssessmentProps = {
  data: IInterviewResult;
};

export function InterviewSessionTechnicalAssessment({ data }: TechnicalAssessmentProps) {
  const { result } = data;

  return (
    <Card className="shadow-none">
      <CardHeader className="border-b">
        <CardTitle>Technical & Behavioral Assessment</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-6 md:grid-cols-2">
          <div>
            <h4 className="mb-2 flex items-center leading-none font-semibold">
              <MessageSquare className="mr-2 h-4 w-4" />
              Communication Skills
            </h4>
            <p className="text-muted-foreground text-sm text-balance">
              {result.communication_skills}
            </p>
          </div>
          <div>
            <h4 className="mb-2 font-semibold">Cognitive Insights</h4>
            <p className="text-muted-foreground text-sm text-balance">
              {result.cognitive_insights}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
