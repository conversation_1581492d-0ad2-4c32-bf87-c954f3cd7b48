import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { IInterviewResult } from "@/types/response.type";
import { Camera } from "lucide-react";

type VideoAssessmentProps = {
  data: IInterviewResult;
};

export function InterviewSessionVideoAssessment({ data }: VideoAssessmentProps) {
  const { result } = data;

  return (
    <Card className="shadow-none">
      <CardHeader className="border-b">
        <CardTitle className="flex items-center leading-none">
          <Camera className="mr-2 h-5 w-5" />
          Video Assessment
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 grid-cols-2">
          <div className="md:space-y-2">
            <div>
              <span className="text-sm font-medium">Eye Contact:</span>
              <span className="ml-2 text-sm text-muted-foreground">{result.eye_contact}</span>
            </div>
            <div>
              <span className="text-sm font-medium">Face Visibility:</span>
              <span className="ml-2 text-sm text-muted-foreground">{result.face_visibility}</span>
            </div>
            <div>
              <span className="text-sm font-medium">Expression:</span>
              <span className="ml-2 text-sm text-muted-foreground">{result.general_expression}</span>
            </div>
          </div>
          <div className="md:space-y-2">
            <div>
              <span className="text-sm font-medium">Camera Quality:</span>
              <span className="ml-2 text-sm text-muted-foreground">{result.camera_quality}</span>
            </div>
            <div>
              <span className="text-sm font-medium">Camera Angle:</span>
              <span className="ml-2 text-sm text-muted-foreground">{result.camera_perspective}</span>
            </div>
            <div>
              <span className="text-sm font-medium">Multiple Faces:</span>
              <span className="ml-2 text-sm text-muted-foreground">{result.multiple_faces}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
