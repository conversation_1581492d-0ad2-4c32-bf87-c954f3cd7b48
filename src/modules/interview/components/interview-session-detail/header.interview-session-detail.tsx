"use client";

import { useEffect, useState } from "react";
import { Copy, Download, Mail, Share2 } from "lucide-react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { IInterviewResult } from "@/types/response.type";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { generateCodeChallenge, generateCodeVerifier } from "@/utils/code-exchange";
import { FaLinkedin, FaTwitter } from "react-icons/fa";
import { useRouter, useSearchParams } from "next/navigation";

type InterviewSessionHeaderProps = {
  id: string;
  data: IInterviewResult;
};

export function InterviewSessionHeader({ id, data }: InterviewSessionHeaderProps) {
  const [shareDropdownOpen, setShareDropdownOpen] = useState(false);

  const searchParams = useSearchParams()
  const status = searchParams.get("status")
  const router = useRouter()
  useEffect(() => {
    const messages: Record<string, string> = {
      successL: "Success share to LinkedIn!",
      failedL: "Failed to share to LinkedIn!",
      successT: "Success share to Twitter!",
      failedT: "Failed to share to Twitter!",
    }

    if (status && messages[status]) {
      const isError = status.startsWith("failed")
      isError ? toast.error(messages[status]) : toast.success(messages[status])

      const params = new URLSearchParams(searchParams.toString())
      params.delete("status")
      const query = params.toString()
      router.replace(query ? `?${query}` : window.location.pathname)
    }
  }, [status, searchParams, router])



  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      toast.success("Link copied to clipboard!", {
        description: "You can share this link with your team",
      });
    } catch (err) {
      console.error("Failed to copy link:", err);
    }
    setShareDropdownOpen(false);
  };

  const handleEmailShare = () => {
    const subject = `Interview Results - ${data.interview.posisi} at ${data.interview.nama_perusahaan}`;
    const body = `Interview results for ${data.user.email}\nBooking Code: ${data.interview.booking_code}\nFinal Score: ${data.result.final_score}/100\n\nView full results: ${window.location.href}`;
    window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`, "_blank", "noopener,noreferrer");
    setShareDropdownOpen(false);
  };

  const handleLinkedin = () => {
    const clientId = process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID!
    const redirectUri = encodeURIComponent(`${process.env.NEXT_PUBLIC_SITE_URL}/api/thirdparty/linkedin/callback`)
    const scope = "openid profile email w_member_social"
    
    window.open(`https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}&state=${id}`, "_blank", "noopener,noreferrer")
  }

  const handleTwitter = async () => {
    const clientId = process.env.NEXT_PUBLIC_TWITTER_CLIENT_ID!;
    const redirectUri = `${process.env.NEXT_PUBLIC_SITE_URL}/api/thirdparty/twitter/callback`;
    const scope = "tweet.read tweet.write users.read offline.access";
  
    const verifier = generateCodeVerifier();
    document.cookie = `twitter_code_verifier=${verifier}; path=/; samesite=lax`;
    
    const challenge = await generateCodeChallenge(verifier);
    
    window.open(`https://twitter.com/i/oauth2/authorize?response_type=code&client_id=${clientId}&redirect_uri=${encodeURIComponent(
    redirectUri
    )}&scope=${scope}&state=${id}&code_challenge=${challenge}&code_challenge_method=S256`, "_blank", "noopener,noreferrer");
  }

  const handleDownload = () => {
    window.print();
    setShareDropdownOpen(false);
  };

  return (
    <div className="bg-background space-y-6 border-b p-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Interview Results</h1>
          <p className="text-muted-foreground">Booking Code: {id}</p>
        </div>

        <div className="flex items-center gap-3 self-start">
          <Badge>{data.interview.status}</Badge>

          <DropdownMenu open={shareDropdownOpen} onOpenChange={setShareDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                aria-label="Share"
                aria-expanded={shareDropdownOpen}
                aria-controls="share-dropdown"
                title="Share"
                className="flex size-6 items-center bg-transparent"
              >
                <Share2 className="size-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={handleCopyLink} className="flex items-center gap-2">
                <Copy className="h-4 w-4" />
                <span className="leading-none">Copy Link</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleEmailShare} className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span className="leading-none">Share via Email</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleLinkedin} className="flex items-center gap-2">
                <FaLinkedin className="h-4 w-4" />
                <span className="leading-none">Share via linkedin</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleTwitter} className="flex items-center gap-2">
                <FaTwitter className="h-4 w-4" />
                <span className="leading-none">Share via Twitter</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleDownload} className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                <span className="leading-none">Download or Print</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
}
