import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { IInterviewResult } from "@/types/response.type";

type SummaryProps = {
  data: IInterviewResult;
};

export function InterviewSessionSummary({ data }: SummaryProps) {
  const { result, interview } = data;

  return (
    <Card className="shadow-none">
      <CardHeader className="border-b">
        <CardTitle>Final Summary</CardTitle>
      </CardHeader>

      <CardContent>
        <p className="text-muted-foreground text-sm">{result.final_summary}</p>
        <div className="bg-primary-foreground mt-4 rounded-lg p-4">
          <p className="text-primary text-sm">
            <strong>Interview Summary:</strong> {interview.summary}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
