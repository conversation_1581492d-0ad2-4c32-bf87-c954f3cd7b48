import { Briefcase, Building, CalendarDays, TrendingUp } from "lucide-react";

import { Card, CardContent } from "@/components/ui/card";
import { IInterviewResult } from "@/types/response.type";
import { cn } from "@/utils/cn";
import { getScoreColor } from "../../utils";

interface InterviewSessionResultProps {
  className?: string;
  data: IInterviewResult;
}

export function InterviewSessionCards({ data, className }: InterviewSessionResultProps) {
  return (
    <div className={cn("font-sans", className)}>
      {/* Final Score */}
      <Card className="shadow-none">
        <CardContent>
          <div className="flex gap-4">
            <div className="size-fit rounded-md border bg-blue-50 p-4">
              <TrendingUp className="@6x/interview-detail:size-6 size-4 shrink-0 text-blue-600" />
            </div>

            <div>
              <p className="text-muted-foreground text-sm font-medium">Final Score</p>
              <p className={`text-xl font-bold ${getScoreColor(data.result.final_score)}`}>
                {data.result.final_score}/100
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interview Date */}
      <Card className="shadow-none">
        <CardContent>
          <div className="flex gap-4">
            <div className="size-fit rounded-md border bg-green-50 p-4">
              <CalendarDays className="@6x/interview-detail:size-6 size-4 shrink-0 text-green-600" />
            </div>
            <div className="truncate">
              <p className="text-muted-foreground text-sm font-medium">Interview Date</p>
              <p
                className="truncate font-semibold"
                aria-label={data.interview.date}
                title={data.interview.date}
              >
                {data.interview.date}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interview Position */}
      <Card className="shadow-none">
        <CardContent>
          <div className="flex gap-4">
            <div className="size-fit rounded-md border bg-purple-50 p-4">
              <Briefcase className="@6x/interview-detail:size-6 size-4 shrink-0 text-purple-600" />
            </div>
            <div className="truncate">
              <p className="text-muted-foreground text-sm font-medium">Position</p>
              <p
                className="truncate font-semibold"
                aria-label={data.interview.posisi}
                title={data.interview.posisi}
              >
                {data.interview.posisi}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interview Company */}
      <Card className="shadow-none">
        <CardContent>
          <div className="flex gap-4">
            <div className="size-fit rounded-md border bg-orange-50 p-4">
              <Building className="@6x/interview-detail:size-6 size-4 shrink-0 text-orange-600" />
            </div>
            <div className="truncate">
              <p className="text-muted-foreground text-sm font-medium">Company</p>
              <p
                className="truncate font-semibold"
                aria-label={data.interview.nama_perusahaan || "-"}
                title={data.interview.nama_perusahaan || "-"}
              >
                {data.interview.nama_perusahaan || "-"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
