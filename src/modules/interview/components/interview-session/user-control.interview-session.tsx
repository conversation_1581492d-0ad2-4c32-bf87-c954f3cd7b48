import { <PERSON><PERSON>, MicOff, PhoneOff, Video, VideoOff } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useAudioProcessor } from "@/hooks/use-audio-processor";
import { useCamera } from "@/hooks/use-camera";
import { ConnectionStatus } from "@/hooks/use-websocket";

import { cn } from "@/utils/cn";

type UserControlProps = {
  camera: ReturnType<typeof useCamera>;
  audio: ReturnType<typeof useAudioProcessor>;
  className?: string;
  status: ConnectionStatus;
  endInterview: () => void;
  disable?: boolean;
};

export function InterviewSessionUserControl({
  camera,
  audio,
  className,
  status,
  endInterview,
  disable,
}: UserControlProps) {
  return (
    <Card className={cn("bg-sidebar flex w-full items-center gap-4 rounded-b-none", className)}>
      <CardContent className="flex items-center justify-center gap-6 md:gap-8">
        <div className="flex flex-col items-center gap-2">
          <Button
            variant={camera.isEnabled ? "outline" : "destructive"}
            size="icon"
            onClick={camera.toggleVideo}
            disabled={status === "connecting" || disable}
            className="size-12 rounded-full md:size-14"
          >
            {camera.isEnabled ? (
              <Video className="size-5 md:size-6" />
            ) : (
              <VideoOff className="size-5 md:size-6" />
            )}
          </Button>
        </div>

        <div className="flex flex-col items-center gap-2">
          <Button
            size="icon"
            variant={audio.isRecording ? "outline" : "destructive"}
            onClick={audio.toggleAudio}
            disabled={status === "connecting" || disable}
            className="size-12 rounded-full md:size-14"
          >
            {audio.isRecording ? (
              <Mic className="size-5 md:size-6" />
            ) : (
              <MicOff className="size-5 md:size-6" />
            )}
          </Button>
        </div>

        <div className="flex flex-col items-center gap-2">
          <Button
            size="icon"
            variant={"destructive"}
            onClick={endInterview}
            disabled={status === "connecting" || disable}
            className="size-12 rounded-full md:size-14"
          >
            <PhoneOff className="size-5 md:size-6" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
