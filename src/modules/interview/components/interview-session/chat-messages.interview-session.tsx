import { AnimatePresence } from "motion/react";
import { useEffect, useRef } from "react";

import { ChatMessage } from "../chat-message";

import { Card, CardAction, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Message } from "@/hooks/use-chat-messages";
import { cn } from "@/utils/cn";
import { LoadingBubble, TurnStatus } from "@/components/shared/loading-bubble";
import { Button } from "@/components/ui/button";

interface ChatMessagesProps {
  messages: Message[];
  isLoading: boolean;
  turnStatus: TurnStatus;
  onEndToTalk: () => void;
  className?: string;
}

export function InterviewSessionChatMessages({
  messages,
  isLoading,
  turnStatus,
  onEndToTalk,
  className,
}: ChatMessagesProps) {
  const chatEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages, isLoading, turnStatus]);

  return (
    <div
      key="chat-messages"
      className={cn("relative flex max-h-[calc(100vh-8rem-1px)] max-w-[400px] flex-1", className)}
    >
      <Card className="h-full w-full flex-1 overflow-hidden rounded-none rounded-tl-xl rounded-tr-xl shadow-none md:rounded-tr-none">
        <CardHeader>
          <CardTitle>Live Chat</CardTitle>
          <CardAction>
            <Button disabled={isLoading || turnStatus === "ai"} onClick={onEndToTalk}>
              End To Talk
            </Button>
          </CardAction>
        </CardHeader>
        <CardContent className="no-scrollbar flex h-full flex-col gap-4 overflow-auto">
          <AnimatePresence mode="popLayout">
            {messages.map((msg, idx) => (
              <ChatMessage key={idx} message={msg} />
            ))}
            {isLoading && <LoadingBubble turnStatus={turnStatus} />}
          </AnimatePresence>
          <div ref={chatEndRef} />
        </CardContent>
      </Card>
    </div>
  );
}
