import { Loader2, VideoOff } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";

import { Card } from "@/components/ui/card";
import { useCamera } from "@/hooks/use-camera";
import { cn } from "@/utils/cn";

interface InterviewSessionCameraUserProps {
  camera: ReturnType<typeof useCamera>;
  className?: string;
  isMobile: boolean;
}

export function InterviewSessionCameraUser({ camera, className }: InterviewSessionCameraUserProps) {
  return (
    <Card
      className={cn(
        "border-muted-foreground relative aspect-video origin-top-left overflow-hidden p-0 shadow-none transition-all md:origin-bottom-left",
        camera.isEnabled ? "opacity-100" : "opacity-0 md:opacity-100",
        className,
      )}
    >
      <AnimatePresence key="loading-camera">
        {camera.isLoading && (
          <motion.div className="bg-background absolute inset-0 z-10 flex items-center justify-center">
            <Loader2 className="size-8 animate-spin" strokeWidth={1.5} />
          </motion.div>
        )}
      </AnimatePresence>
      <video
        ref={camera.ref}
        className="size-full object-cover"
        autoPlay
        muted
        playsInline
        disablePictureInPicture
        disableRemotePlayback
      />

      {!camera.isEnabled && (
        <div className="bg-gradient-purple absolute inset-0 z-10 flex items-center justify-center text-white dark:brightness-70">
          <VideoOff className="size-6 md:size-8" />
        </div>
      )}
    </Card>
  );
}
