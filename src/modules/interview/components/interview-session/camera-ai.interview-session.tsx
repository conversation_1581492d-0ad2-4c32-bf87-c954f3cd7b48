import { Slider } from "@/components/ui/slider";
import { duration, easing } from "@/utils/animation";
import { Loader2, Volume, Volume1, Volume2, VolumeOff } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import { useCallback, useEffect, useState } from "react";

type InterviewSessionCameraAIProps = {
  isLoading: boolean;
  idleVideoRef: React.RefObject<HTMLVideoElement | null>;
  streamVideoRef: React.RefObject<HTMLVideoElement | null>;
};

export function InterviewSessionCameraAI({
  idleVideoRef,
  streamVideoRef,
  isLoading,
}: InterviewSessionCameraAIProps) {
  const [volume, setVolume] = useState(100);
  const [isHover, setIsHover] = useState(false);
  const [isMuted, setIsMuted] = useState(false);

  // Update video volume when volume state changes
  useEffect(() => {
    if (streamVideoRef.current) {
      streamVideoRef.current.volume = volume / 100;
      streamVideoRef.current.muted = isMuted || volume === 0;
    }
  }, [volume, isMuted, streamVideoRef]);

  const renderSpeaker = useCallback(() => {
    if (volume === 0) return <VolumeOff className="size-4 shrink-0" />;
    if (volume <= 25) return <Volume className="size-4 shrink-0" />;
    if (volume <= 50) return <Volume1 className="size-4 shrink-0" />;
    return <Volume2 className="size-4 shrink-0" />;
  }, [volume]);

  return (
    <div className="relative size-full">
      <AnimatePresence mode="wait">
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="bg-background absolute inset-0 z-10 flex items-center justify-center"
          >
            <div className="text-primary flex flex-col items-center justify-center gap-4 opacity-80">
              <p className="text-lg font-bold">Connecting to AI Interviewer</p>
              <Loader2 className="size-12 animate-spin" strokeWidth={1.5} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      <video
        ref={idleVideoRef}
        src="/videos/avatar-idle.mp4"
        autoPlay
        loop
        disablePictureInPicture
        className="absolute top-0 left-0 size-full object-cover transition-opacity duration-500"
        style={{ opacity: 1 }}
      />
      <video
        ref={streamVideoRef}
        autoPlay
        disablePictureInPicture
        className="absolute top-0 left-0 size-full object-cover transition-opacity duration-500"
        style={{ opacity: 0 }}
      />

      <div
        onMouseLeave={() => setIsHover(false)}
        className="absolute top-0 right-0 z-40 m-4 flex flex-col items-center justify-center gap-4 md:top-auto md:bottom-0"
      >
        <AnimatePresence key={isMuted ? "muted" : "unmuted"}>
          {isHover && (
            <motion.div
              key="volume-slider"
              initial={{ opacity: 0, y: 20, scaleY: 0.8 }}
              animate={{
                opacity: 1,
                y: 0,
                scaleY: 1,
                transition: { duration: duration.short, ease: easing.in },
              }}
              exit={{
                opacity: 0,
                y: 20,
                transition: { duration: duration.short, ease: easing.in },
              }}
            >
              <Slider
                defaultValue={[volume]}
                min={0}
                max={100}
                onValueChange={(value) => {
                  setVolume(value[0]);

                  if (value[0] > 0) {
                    setIsMuted(false);
                  }
                }}
                orientation="vertical"
              />
            </motion.div>
          )}
        </AnimatePresence>
        <div className="rounded-full opacity-80 transition-opacity hover:opacity-100">
          <button
            type="button"
            onClick={() => {
              setIsMuted(!isMuted);
              setVolume(volume === 0 ? 100 : 0);
            }}
            onMouseEnter={() => setIsHover(true)}
            className="bg-primary text-primary-foreground rounded-full p-2.5"
          >
            {renderSpeaker()}
          </button>
        </div>
      </div>
    </div>
  );
}
