import { apiWebSocket } from "@/lib/api-websocket";
import { ISpeechMeticsResponse } from "@/types/response.type";
import { tryCatch } from "@/utils/try-catch";

export async function getSpeechmatics(apiKey: string, bookingCode: string) {
  const [res, err] = await tryCatch(
    apiWebSocket({
      url: "/api/speechmatics/init/",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      apiKey,
      body: JSON.stringify({ booking_code: bookingCode }),
      cache: "no-store",
    }),
  );

  if (err) {
    return null;
  }

  const response = await res.json();

  if (!res.ok) {
    console.log(response);
    return null;
  }

  return response as ISpeechMeticsResponse;
}
