import { apiWebSocket } from "@/lib/api-websocket";
import { IDidResponse } from "@/types/response.type";
import { tryCatch } from "@/utils/try-catch";

export async function getDid(apiKey: string) {
  const [res, err] = await tryCatch(
    apiWebSocket({
      url: "/api/did/init",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      apiKey,
    }),
  );

  if (err) {
    return null;
  }

  const response = await res.json();

  if (!res.ok) {
    console.log(response);
    return null;
  }

  return response as IDidResponse;
}
