import { apiWebSocket } from "@/lib/api-websocket";
import { GladiaResponse } from "@/types/response.type";
import { tryCatch } from "@/utils/try-catch";

export async function getGladia(apiKey: string) {
  const [res, err] = await tryCatch(
    apiWebSocket({
      url: "/api/gladia/init",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: "{}",
      apiKey,
    }),
  );

  if (err) {
    return null;
  }

  return (await res.json()) as GladiaResponse;
}
