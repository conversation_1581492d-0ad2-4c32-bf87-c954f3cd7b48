import { apiServer } from "@/lib/api-server";
import { IInterviewResult } from "@/types/response.type";
import { tryCatch } from "@/utils/try-catch";

export async function getResult(id: string) {
  const [res, err] = await tryCatch(apiServer({ url: `/api/interview/report/${id}` }));

  if (err) {
    return null;
  }

  const response = await res.json();

  if (!res.ok) {
    return null;
  }

  return response as IInterviewResult;
  
}
