"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { motion } from "motion/react";
import { useCallback, useEffect } from "react";
import { CheckCircle, Users } from "lucide-react";

import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";

import { useWebSocket } from "@/hooks/use-websocket";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";

type Message =
  | {
      type: "queue_update";
      queue_size: number;
      position: number;
    }
  | {
      type: "start_interview";
      message: string;
    };

export function QueueInterviewView({ url }: { url: string }) {
  const router = useRouter();
  const ws = useWebSocket<Message>(url, {
    onOpen: async () => {
      console.log("Connected");
    },

    onMessage: (data) => {
      console.log(data);
    },
    onClose: async () => {
      console.log("Disconnected");
    },
    onError: (error) => {
      console.log(error);
    },
  });

  const progressPercentage = useCallback(() => {
    if (ws.lastMessage?.type === "queue_update") {
      const position = Math.abs(ws.lastMessage.position || 1);
      const queueSize = ws.lastMessage.queue_size || 1;
      return (position / queueSize) * 100;
    }

    return 0;
  }, [ws.lastMessage]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      ws.connect();
    }
  }, []);

  if (ws.lastMessage?.type === "start_interview") {
    return (
      <div className="bg-background flex min-h-screen items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-green-600">Ready to Start!</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 text-center">
            <p className="text-muted-foreground text-lg">{ws.lastMessage.message}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
      className="flex size-full flex-1 items-center justify-center p-4 md:p-6"
    >
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full">
            <Image src="/web-app-manifest-512x512.png" alt="Logo" width={64} height={64} />
          </div>
          <CardTitle className="text-2xl font-bold">You&apos;re in Queue</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="relative text-center">
            <div className="text-primary bg-muted/90 relative z-10 mx-auto mb-2 flex size-20 items-center justify-center rounded-full border text-4xl font-bold backdrop-blur-xs">
              #{Math.abs(ws.lastMessage?.position || 1)}
            </div>
            <div className="bg-primary absolute top-1/2 left-1/2 size-14 -translate-x-1/2 -translate-y-1/2 animate-ping rounded-full" />
          </div>

          <div className="space-y-2">
            <p className="text-muted-foreground text-center">Your position in queue</p>
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(progressPercentage())}%</span>
            </div>
            <Progress value={progressPercentage()} className="h-2" />
          </div>

          <div className="text-muted-foreground flex items-center justify-center gap-2">
            <Users className="h-4 w-4" />
            <span className="text-sm">{ws.lastMessage?.queue_size || 0} people in total queue</span>
          </div>

          <div className="bg-muted/50 rounded-lg border p-4 text-center">
            <p className="text-muted-foreground text-sm">
              Estimated wait time:{" "}
              <span className="font-medium">{(ws.lastMessage?.position || 0) * 2} minutes</span>
            </p>
          </div>

          <Button
            disabled={progressPercentage() === 0}
            onClick={() => router.refresh()}
            className="w-full"
          >
            Start Interview
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
}
