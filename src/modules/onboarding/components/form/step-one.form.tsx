import { RiBriefcase4Fill, RiGraduationCapFill, RiRouteFill } from "@remixicon/react";
import { ChevronRightIcon, User2 } from "lucide-react";

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/utils/cn";

export function StepOneForm({
  who_are_you,
  onValueChange,
}: {
  who_are_you: string;
  onValueChange: (value: string) => void;
}) {
  return (
    <div className="flex flex-col items-center justify-center">
      <div className="bg-gradient-purple clamp-[size,10,20] mb-4 grid place-content-center rounded-full">
        <User2 className="text-primary-foreground clamp-[size,6,10]" />
      </div>
      <h1 className="clamp-[text,lg,2xl] mb-1 font-bold">Siapa Kamu?</h1>
      <p className="text-muted-foreground mb-6 text-center">
        <PERSON><PERSON><PERSON> kate<PERSON>i yang paling sesuai dengan kondisi Anda saat ini
      </p>

      <RadioGroup
        name="who_are_you"
        className="w-full gap-2"
        value={who_are_you}
        onValueChange={onValueChange}
      >
        <div className="border-input has-data-[state=checked]:border-green-500/50 relative flex w-full items-start gap-2 rounded-md border p-4 outline-none">
          <RadioGroupItem
            value="Fresh Graduate"
            className="absolute size-full opacity-0 after:absolute after:inset-0"
          />
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-green grid size-12 shrink-0 place-content-center rounded-full">
                <RiGraduationCapFill className="size-6 text-white" />
              </div>

              <div>
                <p className="text-sm font-medium">Fresh Graduate</p>
                <p className="text-muted-foreground text-sm">
                  Baru lulus dan mencari pekerjaan pertama
                </p>
              </div>
            </div>

            <ChevronRightIcon
              className={cn("shrink-0 text-muted-foreground size-4", {
                "text-green-500": who_are_you === "Fresh Graduate",
              })}
            />
          </div>
        </div>

        <div className="border-input has-data-[state=checked]:border-orange-500/50 relative flex w-full items-start gap-2 rounded-md border p-4 outline-none">
          <RadioGroupItem
            value="Career Switcher"
            className="absolute size-full opacity-0 after:absolute after:inset-0"
          />
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-orange grid size-12 shrink-0 place-content-center rounded-full">
                <RiRouteFill className="size-6 text-white" />
              </div>

              <div>
                <p className="text-sm font-medium">Career Switcher</p>
                <p className="text-muted-foreground text-sm">
                  Ingin beralih ke bidang atau industri baru
                </p>
              </div>
            </div>

            <ChevronRightIcon
              className={cn("shrink-0 text-muted-foreground size-4", {
                "text-orange-500": who_are_you === "Career Switcher",
              })}
            />
          </div>
        </div>

        <div className="border-input has-data-[state=checked]:border-primary/50 relative flex w-full items-start gap-2 rounded-md border p-4 outline-none">
          <RadioGroupItem
            value="Job Seeker Berpengalaman"
            className="absolute size-full opacity-0 after:absolute after:inset-0"
          />
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-purple grid size-12 shrink-0 place-content-center rounded-full">
                <RiBriefcase4Fill className="size-6 text-white" />
              </div>

              <div>
                <p className="text-sm font-medium">Job Seeker Berpengalaman</p>
                <p className="text-muted-foreground text-sm">
                  Sudah berpengalaman dan mencari peluang baru
                </p>
              </div>
            </div>

            <ChevronRightIcon
              className={cn("shrink-0 text-muted-foreground size-4", {
                "text-primary": who_are_you === "Job Seeker Berpengalaman",
              })}
            />
          </div>
        </div>
      </RadioGroup>
    </div>
  );
}
