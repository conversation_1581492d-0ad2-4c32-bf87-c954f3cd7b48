import {
  RiArrowLeftRightLine,
  Ri<PERSON><PERSON>ondFill,
  R<PERSON><PERSON><PERSON>tPulseFill,
  RiLeafFill,
  R<PERSON><PERSON>ightbulbFill,
  RiLineChartLine,
  RiVipCrown2Fill,
} from "@remixicon/react";
import { Check, ChevronRightIcon } from "lucide-react";

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/utils/cn";

export function StepThreeForm({
  preparation_level,
  onValueChange,
}: {
  preparation_level: string;
  onValueChange: (value: string) => void;
}) {
  return (
    <div className="flex flex-col items-center justify-center">
      <div className="bg-gradient-purple clamp-[size,10,20] mb-4 grid place-content-center rounded-full">
        <div className="border-primary-foreground rounded-full border-2 p-3 text-lg leading-none font-bold">
          <Check className="size-6 text-white" />
        </div>
      </div>
      <h1 className="clamp-[text,lg,2xl] mb-1 font-bold">Sejauh Mana <PERSON>panmu?</h1>
      <p className="text-muted-foreground mb-2 text-center">
        Bantu kami memahami level pengalamanmu
      </p>

      <div className="clamp-[text,xs,base] mb-6 flex w-full items-center justify-center gap-2 rounded-md border border-blue-100 bg-blue-50 px-4 py-3 text-blue-500">
        <RiLightbulbFill className="size-4 shrink-0" />
        Jujur saja! Ini akan membantu kami menyesuaikan latihan yang tepat untukmu.
      </div>

      <RadioGroup
        name="preparation_level"
        className="w-full gap-2"
        value={preparation_level}
        onValueChange={onValueChange}
      >
        <div className="border-input relative flex w-full items-start gap-2 rounded-md border p-4 outline-none has-data-[state=checked]:border-red-500/50">
          <RadioGroupItem
            value="Belum Pernah Latihan Interview"
            className="absolute top-0 left-0 size-full rounded-none opacity-0 after:absolute after:inset-0"
          />
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-red grid size-12 shrink-0 place-content-center rounded-full">
                <RiLeafFill className="size-6 text-white" />
              </div>

              <div>
                <p className="text-sm font-medium">Belum Pernah Latihan Interview</p>
                <div className="flex items-center gap-2">
                  <p className="text-muted-foreground text-xs md:text-sm">
                    Mulai dari dasar dan belajar step by step
                  </p>
                  <div className="hidden size-fit rounded-full border border-red-100 bg-red-50 px-2 py-0.5 text-xs text-red-500 md:block">
                    25 XP
                  </div>
                </div>
              </div>
            </div>

            <ChevronRightIcon
              className={cn("text-muted-foreground size-4 shrink-0", {
                "text-red-500": preparation_level === "Belum Pernah Latihan Interview",
              })}
            />
          </div>
        </div>

        <div className="border-input relative flex w-full items-start gap-2 rounded-md border p-4 outline-none has-data-[state=checked]:border-green-500/50">
          <RadioGroupItem
            value="Cukup Percaya Diri, Tinggal Polish"
            className="absolute top-0 left-0 size-full rounded-none opacity-0 after:absolute after:inset-0"
          />
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-green grid size-12 shrink-0 place-content-center rounded-full">
                <RiDiamondFill className="size-6 text-white" />
              </div>

              <div>
                <p className="text-sm font-medium">Cukup Percaya Diri, Tinggal Polish</p>
                <div className="flex items-center gap-2">
                  <p className="text-muted-foreground text-xs md:text-sm">
                    Sempurnakan teknik dan strategi advanced
                  </p>
                  <div className="hidden size-fit rounded-full border border-green-100 bg-green-50 px-2 py-0.5 text-xs text-green-500 md:block">
                    25 XP
                  </div>
                </div>
              </div>
            </div>

            <ChevronRightIcon
              className={cn("text-muted-foreground size-4 shrink-0", {
                "text-green-500": preparation_level === "Cukup Percaya Diri, Tinggal Polish",
              })}
            />
          </div>
        </div>

        <div className="border-input relative flex w-full items-start gap-2 rounded-md border p-4 outline-none has-data-[state=checked]:border-blue-500/50">
          <RadioGroupItem
            value="Sudah Pernah Coba, Tapi Masih Grogi"
            className="absolute top-0 left-0 size-full rounded-none opacity-0 after:absolute after:inset-0"
          />
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-blue grid size-12 shrink-0 place-content-center rounded-full">
                <RiHeartPulseFill className="size-6 text-white" />
              </div>

              <div>
                <p className="text-sm font-medium">Sudah Pernah Coba, Tapi Masih Grogi</p>
                <div className="flex items-center gap-2">
                  <p className="text-muted-foreground text-xs md:text-sm">
                    Tingkatkan kepercayaan diri dan kurangi kegugupan
                  </p>
                  <div className="hidden size-fit rounded-full border border-blue-100 bg-blue-50 px-2 py-0.5 text-xs text-blue-500 md:block">
                    25 XP
                  </div>
                </div>
              </div>
            </div>

            <ChevronRightIcon
              className={cn("text-muted-foreground size-4 shrink-0", {
                "text-blue-500": preparation_level === "Sudah Pernah Coba, Tapi Masih Grogi",
              })}
            />
          </div>
        </div>
      </RadioGroup>
    </div>
  );
}
