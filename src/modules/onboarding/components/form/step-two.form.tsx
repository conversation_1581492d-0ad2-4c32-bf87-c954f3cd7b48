import {
  RiArrowLeftRightLine,
  RiLightbulbFill,
  RiLineChartLine,
  RiQuestionMark,
  RiRocketLine,
  RiVipCrown2Fill,
} from "@remixicon/react";
import { ChevronRightIcon } from "lucide-react";

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/utils/cn";

export function StepTwoForm({
  primary_goal,
  onValueChange,
}: {
  primary_goal: string;
  onValueChange: (value: string) => void;
}) {
  return (
    <div className="flex flex-col items-center justify-center">
      <div className="bg-gradient-purple clamp-[size,10,20] mb-4 grid place-content-center rounded-full">
        <div className="border-primary-foreground clamp-[p,2,3] rounded-full border-2 border-dashed text-lg leading-none font-bold">
          <RiQuestionMark className="clamp-[size,3,6] text-white" />
        </div>
      </div>
      <h1 className="clamp-[text,base,2xl] mb-1 font-bold">Apa Tujuan Utam<PERSON> Kamu?</h1>
      <p className="text-muted-foreground mb-2 text-center text-xs md:text-base">
        Pilih yang paling sesuai dengan goals kamu saat ini
      </p>

      <div className="clamp-[text,xs,base] mb-6 flex w-full items-center justify-center gap-2 rounded-md border border-blue-100 bg-blue-50 px-4 py-3 text-blue-500">
        <RiLightbulbFill className="size-4 shrink-0" />
        Dengan tahu tujuanmu, kita bisa kasih rekomendasi lebih tepat.
      </div>

      <RadioGroup
        name="who_are_you"
        className="w-full gap-2"
        value={primary_goal}
        onValueChange={onValueChange}
      >
        <div className="border-input relative flex w-full items-start gap-2 rounded-md border p-4 outline-none has-data-[state=checked]:border-green-500/50">
          <RadioGroupItem
            value="Mendapat Pekerjaan Pertama"
            className="absolute top-0 left-0 z-50 size-full rounded-none opacity-0 after:absolute after:inset-0"
          />
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-green grid size-12 shrink-0 place-content-center rounded-full">
                <RiRocketLine className="size-6 rotate-45 text-white" />
              </div>

              <div>
                <p className="text-sm font-medium">Mendapat Pekerjaan Pertama</p>
                <div className="flex items-center gap-2">
                  <p className="text-muted-foreground text-xs md:text-sm">
                    Mulai karier dan dapatkan pekerjaan impian
                  </p>
                  <div className="hidden size-fit rounded-full border border-green-100 bg-green-50 px-2 py-0.5 text-xs text-green-500 md:block">
                    25 XP
                  </div>
                </div>
              </div>
            </div>

            <ChevronRightIcon
              className={cn("text-muted-foreground size-4 shrink-0", {
                "text-green-500": primary_goal === "Mendapat Pekerjaan Pertama",
              })}
            />
          </div>
        </div>

        <div className="border-input relative flex w-full items-start gap-2 rounded-md border p-4 outline-none has-data-[state=checked]:border-blue-500/50">
          <RadioGroupItem
            value="Upgrade Skill Interview"
            className="absolute top-0 left-0 z-50 size-full rounded-none opacity-0 after:absolute after:inset-0"
          />
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-blue grid size-12 shrink-0 place-content-center rounded-full">
                <RiLineChartLine className="size-6 text-white" />
              </div>

              <div>
                <p className="text-sm font-medium">Upgrade Skill Interview</p>
                <div className="flex items-center gap-2">
                  <p className="text-muted-foreground text-xs md:text-sm">
                    Tingkatkan kemampuan interview jadi lebih percaya diri
                  </p>
                  <div className="hidden size-fit rounded-full border border-blue-100 bg-blue-50 px-2 py-0.5 text-xs text-blue-500 md:block">
                    25 XP
                  </div>
                </div>
              </div>
            </div>

            <ChevronRightIcon
              className={cn("text-muted-foreground size-4 shrink-0", {
                "text-blue-500": primary_goal === "Upgrade Skill Interview",
              })}
            />
          </div>
        </div>

        <div className="border-input relative flex w-full items-start gap-2 rounded-md border p-4 outline-none has-data-[state=checked]:border-orange-500/50">
          <RadioGroupItem
            value="Ganti Karier ke Bidang Baru"
            className="absolute top-0 left-0 z-50 size-full rounded-none opacity-0 after:absolute after:inset-0"
          />
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-orange grid size-12 shrink-0 place-content-center rounded-full">
                <RiArrowLeftRightLine className="size-6 text-white" />
              </div>

              <div>
                <p className="text-sm font-medium">Ganti Karier ke Bidang Baru</p>
                <div className="flex items-center gap-2">
                  <p className="text-muted-foreground text-xs md:text-sm">
                    Transisi ke industri atau role yang berbeda
                  </p>
                  <div className="hidden size-fit rounded-full border border-orange-100 bg-orange-50 px-2 py-0.5 text-xs text-orange-500 md:block">
                    25 XP
                  </div>
                </div>
              </div>
            </div>

            <ChevronRightIcon
              className={cn("text-muted-foreground size-4 shrink-0", {
                "text-orange-500": primary_goal === "Ganti Karier ke Bidang Baru",
              })}
            />
          </div>
        </div>

        <div className="border-input has-data-[state=checked]:border-primary/50 relative flex w-full items-start gap-2 rounded-md border p-4 outline-none">
          <RadioGroupItem
            value="Naik Level / Promosi Karier"
            className="absolute top-0 left-0 z-50 size-full rounded-none opacity-0 after:absolute after:inset-0"
          />
          <div className="flex w-full items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-gradient-purple grid size-12 shrink-0 place-content-center rounded-full">
                <RiVipCrown2Fill className="size-6 text-white" />
              </div>

              <div>
                <p className="text-sm font-medium">Naik Level / Promosi Karier</p>
                <div className="flex items-center gap-2">
                  <p className="text-muted-foreground text-xs md:text-sm">
                    Capai posisi yang lebih tinggi dan tanggung jawab besar
                  </p>
                  <div className="hidden size-fit rounded-full border border-purple-100 bg-purple-50 px-2 py-0.5 text-xs text-purple-500 md:block">
                    25 XP
                  </div>
                </div>
              </div>
            </div>

            <ChevronRightIcon
              className={cn("text-muted-foreground size-4 shrink-0", {
                "text-primary": primary_goal === "Naik Level / Promosi Karier",
              })}
            />
          </div>
        </div>
      </RadioGroup>
    </div>
  );
}
