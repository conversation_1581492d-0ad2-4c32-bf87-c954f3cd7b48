"use client";

import { startTransition, useActionState, useEffect, useState } from "react";
import { AnimatePresence, motion, usePresenceData, wrap } from "motion/react";
import { <PERSON>Left, ArrowRight } from "lucide-react";
import { RiStarFill } from "@remixicon/react";

import { StepOneForm } from "../components/form/step-one.form";
import { StepTwoForm } from "../components/form/step-two.form";
import { StepThreeForm } from "../components/form/step-three.form";
import { onboardingAction } from "../services/onboarding.service";

import { Button } from "@/components/ui/button";
import { easing } from "@/utils/animation";
import { Count } from "@/components/animation/count.animation";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";

import { initialActionState } from "@/types/action.type";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";

const items = [1, 2, 3, 4];

export function OnboardingView() {
  const router = useRouter();
  const { data: session, update } = useSession();

  const [state, action, pending] = useActionState(onboardingAction, initialActionState);
  const [selectedItem, setSelectedItem] = useState(items[0]);
  const [xp, setXp] = useState(0);
  const [direction, setDirection] = useState<1 | -1>(1);
  const [payload, setPayload] = useState({
    who_are_you: "",
    primary_goal: "",
    preparation_level: "",
  });

  function setSlide(newDirection: 1 | -1) {
    const nextItem = wrap(1, items.length, selectedItem + newDirection);
    setSelectedItem(nextItem);
    setDirection(newDirection);
  }

  function setIndexSlide(newIdx: number) {
    const nextItem = wrap(1, items.length, newIdx);
    setSelectedItem(nextItem);
    setDirection(newIdx > selectedItem ? 1 : -1);
  }

  const handleWhoAreYouChange = (value: string) => {
    if (!payload.who_are_you) {
      setXp((prev) => prev + 25);
    }
    setPayload((prev) => ({ ...prev, who_are_you: value }));
    setIndexSlide(2);
  };

  const handlePrimaryGoalChange = (value: string) => {
    if (!payload.primary_goal) {
      setXp((prev) => prev + 25);
    }
    setPayload((prev) => ({ ...prev, primary_goal: value }));
    setIndexSlide(3);
  };

  const handlePreparationLevelChange = (value: string) => {
    if (!payload.preparation_level) {
      setXp((prev) => prev + 25);
    }
    setPayload((prev) => ({ ...prev, preparation_level: value }));
  };

  const handleDisableNextButton = () => {
    if (selectedItem === 1 && !payload.who_are_you) {
      return true;
    }

    if (selectedItem === 2 && !payload.primary_goal) {
      return true;
    }

    if (selectedItem === 3 && !payload.preparation_level) {
      return true;
    }

    return false;
  };

  const handleNextButton = () => {
    if (selectedItem === 3) {
      const newFormData = new FormData();

      for (const key in payload) {
        newFormData.append(key, payload[key as keyof typeof payload] as string);
      }

      startTransition(() => action(newFormData));
    } else {
      setSlide(1);
    }
  };

  useEffect(() => {
    if (state.success && state.message) {
      toast.success(state.message, {
        description: "Anda telah berhasil menyelesaikan onboarding!",
      });
      setXp(100);

      const timeout = setTimeout(() => {
        update({
          ...session?.user,
          is_onboarded: true,
        }).then(() => {
          router.push("/dashboard");
        });
      }, 1000);

      return () => clearTimeout(timeout);
    }

    if (!state.success && state.message) {
      toast.error(state.message, { description: state.errors });
    }
  }, [state.success, state.message, state.errors]);

  return (
    <Card className="h-fit w-full max-w-3xl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <p className="text-muted-foreground text-xs">Progress Onboarding</p>
          <div className="text-primary flex items-center justify-center text-xs font-medium">
            <Count count={selectedItem} duration={0.1} />
            &nbsp;dari 3
          </div>
        </div>
        <div className="bg-muted mb-4 h-1.25 w-full overflow-hidden rounded-full">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${(selectedItem / (items.length - 1)) * 100}%` }}
            transition={{ duration: 0.75, ease: easing.out }}
            className="bg-gradient-purple h-1.25 rounded-full"
          />
        </div>

        <div className="bg-primary/5 space-y-2 rounded-md p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <RiStarFill className="size-4 text-yellow-400" />
              <p className="text-sm font-medium">XP Progress</p>
            </div>

            <div className="text-primary flex items-center justify-center text-sm font-medium">
              <Count count={xp} duration={0.1} />
              /100 XP
            </div>
          </div>

          <div className="bg-background h-1.5 w-full overflow-hidden rounded-full">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${xp || 1}%` }}
              transition={{ duration: 0.75, ease: easing.out }}
              className="bg-gradient-yellow h-1.25 rounded-full"
            />
          </div>
          <p className="text-muted-foreground text-xs">
            Mulai perjalanan Anda untuk mendapatkan XP!
          </p>
        </div>
      </CardHeader>

      <CardContent style={{ clipPath: "inset(0 0 0 0)" }}>
        <AnimatePresence custom={{ direction, index: selectedItem }} initial={false} mode="wait">
          <Slide
            key={selectedItem}
            index={selectedItem}
            steps={[
              <StepOneForm
                key={1}
                who_are_you={payload.who_are_you}
                onValueChange={handleWhoAreYouChange}
              />,
              <StepTwoForm
                key={2}
                primary_goal={payload.primary_goal}
                onValueChange={handlePrimaryGoalChange}
              />,
              <StepThreeForm
                key={3}
                preparation_level={payload.preparation_level}
                onValueChange={handlePreparationLevelChange}
              />,
            ]}
          />
        </AnimatePresence>
      </CardContent>

      <CardFooter className="w-full border-t">
        <div className="flex w-full items-center justify-between">
          <Button
            onClick={() => setSlide(-1)}
            disabled={selectedItem === 1 || pending}
            variant="outline"
          >
            <ArrowLeft className="size-4" />
            Kembali
          </Button>

          <Button onClick={handleNextButton} disabled={handleDisableNextButton() || pending}>
            {selectedItem === items.length - 1 ? <span>Selesai</span> : "Selanjutnya"}
            <ArrowRight className="size-4" />
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}

function Slide({
  ref,
  index,
  steps,
}: {
  ref?: React.Ref<HTMLDivElement>;
  index: number;
  steps: [React.ReactNode, React.ReactNode, React.ReactNode];
}) {
  const { direction } = usePresenceData() as { direction: 1 | -1; index: number };

  const renderIndex = (idx: number) => {
    switch (idx) {
      case 1:
        return steps[0];
      case 2:
        return steps[1];
      case 3:
        return steps[2];
      default:
        return null;
    }
  };

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, x: direction * 50 }}
      animate={{
        opacity: 1,
        x: 0,
        transition: {
          duration: 0.5,
          //   delay: 0.3,
          ease: easing.out,
        },
      }}
      exit={{ opacity: 0, x: direction * -50 }}
    >
      {renderIndex(index)}
    </motion.div>
  );
}
