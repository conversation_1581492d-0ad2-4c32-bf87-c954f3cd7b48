"use server";

import { apiServer } from "@/lib/api-server";
import { rewards } from "@/modules/gamification/services/rewards.service";
import { ActionState } from "@/types/action.type";
import { tryCatch } from "@/utils/try-catch";

type OnboardingActionType = ActionState<string, string>;

export async function onboardingAction(
  state: OnboardingActionType,
  formData: FormData,
): Promise<OnboardingActionType> {
  try {
    const payload = {
      who_are_you: formData.get("who_are_you") as string,
      primary_goal: formData.get("primary_goal") as string,
      preparation_level: formData.get("preparation_level") as string,
    };

    const [response, err] = await tryCatch(
      apiServer({
        method: "POST",
        url: "/api/onboarding/",
        body: JSON.stringify(payload),
      }),
    );

    if (err) {
      return {
        success: false,
        message: "Internal Server Error",
      };
    }

    if (!response.ok) {
      return {
        success: false,
        message: "Gagal menyelesaikan onboarding",
      };
    }

    await rewards("onboarding_complete");

    return {
      success: true,
      message: "<PERSON><PERSON><PERSON>il menyelesaikan onboarding",
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      message: "Unexpected Error",
    };
  }
}
