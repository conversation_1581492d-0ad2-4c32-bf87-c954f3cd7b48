import { strapi } from "@/lib/strapi";
import { StrapiNewsArticle, StrapiResponse } from "@/types/strapi.type";
import { tryCatch } from "@/utils/try-catch";

export const getRelatedNews = async (slug: string, category: string) => {
  const [res, err] = await tryCatch(
    strapi({
      path: `/api/news-articles?sort=publishedAt:desc&pagination[page]=1&pagination[pageSize]=3&filters[slug][$ne]=${slug}&filters[news_category][slug][$eq]=${category}`,
    }),
  );

  if (err || !res.ok) {
    return null;
  }

  const response = (await res.json()) as StrapiResponse<StrapiNewsArticle[]>;
  const data = response.data;

  return data;
};
