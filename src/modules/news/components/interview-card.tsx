import { Calendar, Star, User, Users } from "lucide-react";

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ButtonCard } from "../components/button-card";

export function InterviewCard() {
  return (
    <Card className="mx-auto w-full max-w-5xl overflow-hidden p-0 shadow-none">
      <div className="grid grid-cols-1 md:grid-cols-2">
        <div className="p-8">
          <CardHeader className="mb-4 p-0">
            <div className="flex justify-start">
              <Badge variant="secondary" className="bg-primary text-primary-foreground">
                <Star className="mr-1 h-3 w-3" />
                Eksklusif
              </Badge>
            </div>

            <CardTitle className="bg-gradient-purple mb-4 bg-clip-text text-3xl font-bold text-transparent">
              Wawancara <PERSON>ri<PERSON>
            </CardTitle>

            <CardDescription className="text-lg leading-relaxed">
              Dapatkan kesempatan emas untuk bergabung dengan tim terbaik. Ka<PERSON> mencari talenta luar
              biasa seperti Anda untuk posisi yang menantang dan penuh peluang.
            </CardDescription>
          </CardHeader>

          <CardContent className="mb-6 space-y-4 p-0">
            <div className="text-muted-foreground flex items-center gap-3 text-base">
              <Calendar className="text-primary h-5 w-5" />
              <span>Proses cepat & fleksibel</span>
            </div>

            <div className="text-muted-foreground flex items-center gap-3 text-base">
              <Users className="text-primary h-5 w-5" />
              <span>Interview dengan tim profesional</span>
            </div>
          </CardContent>

          <CardFooter className="p-0">
            <ButtonCard />
          </CardFooter>
        </div>

        <div className="bg-primary/10 flex flex-col items-center justify-center p-8">
          <div className="mb-6 flex flex-col items-center justify-center">
            <div className="bg-primary mb-4 flex size-20 items-center justify-center rounded-full">
              <User className="size-10 text-white" />
            </div>

            <h3 className="text-foreground mb-2 text-center text-xl font-semibold">
              Tim Profesional
            </h3>

            <p className="text-muted-foreground text-center">
              Bertemu langsung dengan hiring manager dan tim yang akan menjadi rekan kerja Anda
            </p>
          </div>

          <div className="border-primary/20 bg-primary/70 rounded-lg border p-4">
            <p className="text-primary-foreground text-sm font-medium">
              💡 Tips: Persiapkan portfolio terbaik Anda dan ceritakan pengalaman yang paling
              berkesan!
            </p>
          </div>
        </div>
      </div>
    </Card>
  );
}
