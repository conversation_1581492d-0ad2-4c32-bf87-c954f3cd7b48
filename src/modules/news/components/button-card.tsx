"use client";

import { ArrowRight } from "lucide-react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";

export function ButtonCard() {
  const { data } = useSession();
  const router = useRouter();

  return (
    <Button
      onClick={() => {
        if (data) {
          router.push("/dashboard/interview/create");
        } else {
          router.push("/login");
        }
      }}
      className="bg-gradient-purple w-full transform py-4 font-semibold text-white transition-all duration-200 hover:shadow-xl"
      size="lg"
    >
      <PERSON><PERSON><PERSON><PERSON> Interview Sekarang
      <ArrowRight className="ml-2 h-5 w-5" />
    </Button>
  );
}
