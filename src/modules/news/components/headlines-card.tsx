import { FC } from "react";
import Image from "next/image";
import Link from "next/link";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

import { cn } from "@/utils/cn";

interface HeadlinesCardProps {
  variant: "update" | "tips" | "komunitas" | "terbaru";
  date: string;
  title: string;
  description: string;
  buttonText: string;
  href: string;
  imageUrl?: string;
  isFeatured?: boolean;
  className?: string;
}

const badgeColors = {
  update: "bg-[#DCFCE7] text-[#16A34A]",
  tips: "bg-[#DBEAFE] text-[#2563EB]",
  komunitas: "bg-[#F3E8FF] text-[#9333EA]",
  terbaru: "bg-[#EDE9FE] text-primary",
};

const badgeLabels = {
  update: "Update",
  tips: "Tips",
  komunitas: "Komunitas",
  terbaru: "Terbaru",
};

const HeadlinesCard: FC<HeadlinesCardProps> = ({
  variant,
  date,
  title,
  description,
  buttonText,
  href,
  imageUrl,
  isFeatured = false,
  className,
}) => {
  return (
    <Card
      className={cn(
        "relative shadow-none transition-all duration-300 hover:shadow-sm",
        isFeatured && "overflow-hidden pt-0 md:hover:shadow-md",
        className,
      )}
    >
      <Link href={href} className="absolute inset-0 z-10 size-full"></Link>
      {imageUrl && (
        <Image
          src={imageUrl}
          alt={title}
          width={1200}
          height={630}
          className={cn(
            "bg-primary relative mb-auto aspect-40/21 object-cover",
            !isFeatured ? "hidden" : "",
          )}
        />
      )}
      <CardHeader className={cn("px-6", isFeatured ? "pt-6" : "")}>
        <div className="flex items-center space-x-2">
          <Badge
            variant="outline"
            className={cn(
              "uppercase",
              badgeColors[variant] ?? "text-muted-foreground border-gray-400",
            )}
          >
            {badgeLabels[variant] ?? variant}
          </Badge>
          <span className="text-sm text-gray-500">{date}</span>
        </div>
        <CardTitle className="mt-2 max-w-[50ch]">{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardFooter className="px-6 pb-6">
        <Button asChild variant="link" className="relative z-20 flex flex-col items-start !p-0">
          <Link href={href} className="p-0 text-left text-purple-600">
            {buttonText} &rarr;
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

export default HeadlinesCard;
