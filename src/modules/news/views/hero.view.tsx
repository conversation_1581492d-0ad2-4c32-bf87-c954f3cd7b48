import { Input } from "@/components/ui/input";
import { StrapiNewsHeroSection } from "@/types/strapi.type";
import { Search } from "lucide-react";
import React from "react";

const HeroNewsView = ({ data: heroSection }: { data: StrapiNewsHeroSection }) => {
  return (
    <section className="from-secondary w-full bg-gradient-to-r to-[#FDF2F8] px-6 py-14">
      <div className="mx-auto max-w-4xl text-center">
        <h1 className="clamp-[text,2xl,5xl] md:mb-4 font-bold text-[#111827]">{heroSection.title}</h1>
        <p className="clamp-[text,sm,xl] mx-auto mb-8 max-w-2xl text-[#374151]">
          {heroSection.description}
        </p>
        <div className="relative mx-auto max-w-md">
          <Search className="absolute top-1/2 left-3 h-5 w-5 -translate-y-1/2 transform text-[#9ca3af]" />
          <Input
            placeholder="Cari artikel..."
            className="border-[#e5e7eb] bg-white pl-10 focus:border-[#7c3aed] focus:ring-[#7c3aed]"
          />
        </div>
      </div>
    </section>
  );
};

export default HeroNewsView;
