"use client";

import { use } from "react";
import { StrapiNewsArticle } from "@/types/strapi.type";
import Image from "next/image";
import { getImageUrl } from "@/utils/image-url";
import Link from "next/link";
import { ArrowRight, FileX } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

export function RelatedNewsView({ promise }: { promise: Promise<StrapiNewsArticle[] | null> }) {
  const data = use(promise);

  return (
    <div className="container mx-auto max-w-5xl py-6">
      <h3 className="mb-6 text-2xl font-bold">Berita Terkait</h3>

      <ul className="grid grid-cols-1 lg:grid-cols-3">
        {data && data.length > 0 ? (
          data.map((item, idx) => {
            return (
              <li
                key={`${item.id}-${idx}`}
                className="bg-secondary aspect-40/21 group relative overflow-hidden rounded-xl border"
              >
                <Link
                  href={`/news/${item.slug}`}
                  className="absolute top-0 right-0 z-20 m-4 size-fit rounded-full p-2 transition-all hover:bg-black/10"
                >
                  <ArrowRight className="text-primary size-6" />
                </Link>

                <Link href={`/news/${item.slug}`} className="absolute inset-0 z-10 size-full" />
                <Image
                  src={getImageUrl(item.thumbnail?.url || "", "strapi")}
                  alt={item.title}
                  fill
                />
                <div className="absolute bottom-0 left-0 w-full bg-black/20 px-4 py-4 backdrop-blur-xs transition-all duration-300 group-hover:bg-black/30 group-hover:backdrop-blur-md md:px-6">
                  <h4 className="text-lg font-bold text-white">{item.title}</h4>
                </div>
              </li>
            );
          })
        ) : (
          <li className="col-span-full">
            <Card className="mx-auto max-w-xl shadow-none">
              <CardContent className="flex flex-col items-center justify-center gap-4">
                <div className="bg-muted rounded-full p-4">
                  <FileX className="size-4 md:size-6" />
                </div>

                <div className="text-center">
                  <h4 className="text-muted-foreground text-2xl font-bold">
                    Tidak ada berita terkait
                  </h4>
                  <p className="text-muted-foreground">
                    Silahkan cari berita lain di halaman berita
                  </p>
                </div>

                <Link
                  href="/news"
                  className="text-background bg-foreground rounded-full px-4 py-2 font-bold hover:underline"
                >
                  Lihat Semua Berita
                </Link>
              </CardContent>
            </Card>
          </li>
        )}
      </ul>
    </div>
  );
}
