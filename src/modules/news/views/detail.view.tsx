import Image from "next/image";
import { Calendar } from "lucide-react";

import { StrapiNewsArticle } from "@/types/strapi.type";
import { getImageUrl } from "@/utils/image-url";

import { RichText } from "@/components/ui/rich-text";

export function DetailNewsView({ data }: { data: StrapiNewsArticle }) {
  return (
    <div className="mx-auto w-full max-w-5xl space-y-6 py-6">
      <article className="bg-card text-card-foreground flex flex-col gap-6 overflow-hidden rounded-xl border pb-6">
        {/* Headline */}
        <div className="relative aspect-video w-full">
          <Image
            src={getImageUrl(data.thumbnail?.url || "", "strapi")}
            alt={data.title}
            fill
            className="bg-primary mb-6 w-full object-cover"
          />
          <div className="absolute bottom-0 left-0 w-full bg-gradient-to-b from-transparent from-10% to-black/20 to-55% px-4 py-6 md:px-6">
            <div className="bg-primary text-primary-foreground mb-2 w-fit shrink-0 rounded-full px-2 py-0.5 text-xs font-medium whitespace-nowrap">
              {data.news_category.badgeText}
            </div>

            <div className="md:flex md:items-end md:justify-between">
              <div className="space-y-2">
                <h1 className="clamp-[text,xl,5xl] font-bold text-white">{data.title}</h1>
                <p className="text-sm text-white">{data.description}</p>
              </div>
              <div className="flex gap-2">
                <div className="flex items-center gap-1 text-white">
                  <Calendar className="size-4" />
                  <p className="text-sm">
                    {new Date(data.publishedAt).toLocaleDateString("id-ID", {
                      day: "numeric",
                      month: "long",
                      year: "numeric",
                    })}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <RichText content={data.content} className="space-y-4 px-4 md:px-6" />
      </article>
    </div>
  );
}
