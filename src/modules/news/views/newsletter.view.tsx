import React from "react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { StrapiNewsNewsletterSection } from "@/types/strapi.type";

const NewsletterNewsView = ({ data: newsletterSection }: { data: StrapiNewsNewsletterSection }) => {
  return (
    <section className="bg-gradient-to-r relative from-[#7c3aed] to-[#ec4899] px-4 py-24">
      <div className="mx-auto max-w-4xl text-center">
        <h2 className="mb-4 text-3xl font-bold text-white">{newsletterSection.title}</h2>
        <p className="mx-auto mb-8 max-w-2xl text-xl text-white/90">
          {newsletterSection.description}
        </p>
        <div className="mx-auto flex max-w-md flex-col gap-3 sm:flex-row">
          <Input placeholder="Masukkan email kamu..." className="flex-1 border-0 bg-white" />
          <Button className="bg-white text-primary hover:bg-gray-100">Subscribe</Button>
        </div>
        <p className="mt-4 text-sm text-white/90">{newsletterSection.description}</p>
      </div>
    </section>
  );
};

export default NewsletterNewsView;
