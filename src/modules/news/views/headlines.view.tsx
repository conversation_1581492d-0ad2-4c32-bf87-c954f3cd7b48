import { FileX2 } from "lucide-react";
import { StrapiNewsArticle, StrapiNewsHeadlineSection } from "@/types/strapi.type";
import { getImageUrl } from "@/utils/image-url";

import HeadlinesCard from "../components/headlines-card";

const HeadlinesNewsView = ({
  data,
  news,
}: {
  data: StrapiNewsHeadlineSection;
  news: StrapiNewsArticle[] | null;
}) => {
  const latest = news?.shift();

  return (
    <section className="px-4 py-16 md:px-6">
      <div className="mb-8 text-center">
        <h2 className="text-3xl font-bold text-black">{data.title}</h2>
        <p className="text-[#4B5563]">{data.description}</p>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
        {latest ? (
          <HeadlinesCard
            variant="terbaru"
            date={new Date(latest.publishedAt).toLocaleDateString("id-ID", {
              day: "numeric",
              month: "long",
              year: "numeric",
            })}
            title={latest.title}
            description={latest.description}
            buttonText="Baca Selengkapnya"
            href={`/news/${latest.slug}`}
            imageUrl={getImageUrl(latest.thumbnail?.url || "", "strapi")}
            isFeatured
            className="col-span-2 md:row-span-1"
          />
        ) : (
          <div className="col-span-full flex flex-col items-center justify-center gap-2 p-6 opacity-80">
            <FileX2 className="text-muted-foreground size-12" />
            <p className="text-muted-foreground text-sm">No latest article</p>
          </div>
        )}

        {news ? (
          <div className="col-span-2 space-y-4">
            {news.map((article) => {
              return (
                <HeadlinesCard
                  key={article.id}
                  variant={
                    article.news_category.badgeText.toLowerCase() as
                      | "update"
                      | "tips"
                      | "komunitas"
                      | "terbaru"
                  }
                  date={new Date(article.publishedAt).toLocaleDateString("id-ID", {
                    day: "numeric",
                    month: "long",
                    year: "numeric",
                  })}
                  title={article.title}
                  description={article.description}
                  buttonText="Baca"
                  imageUrl={getImageUrl(article.thumbnail?.url || "", "strapi")}
                  href={`/news/${article.slug}`}
                />
              );
            })}
          </div>
        ) : (
          <div className="col-span-full flex flex-col items-center justify-center gap-2 p-6 opacity-80">
            <FileX2 className="text-muted-foreground size-12" />
            <p className="text-muted-foreground text-sm">No article</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default HeadlinesNewsView;
