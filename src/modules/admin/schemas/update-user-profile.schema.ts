import { z } from "zod/v4";

export const updateUserProfileSchema = z.object({
  id: z.string(),
  full_name: z.string("<PERSON><PERSON> leng<PERSON> harus diisi").max(50),
  username: z.string("Username harus diisi").max(50),
  email: z.email("Email tidak valid"),
  phone_number: z.string("Nomor telepon harus diisi"),
  date_of_birth: z.iso.datetime("Tanggal lahir tidak valid"),
  gender: z.enum(["Laki-la<PERSON>", "Perempuan"]),
  bio: z.string("Biografi harus diisi").optional(),
  is_staff: z.boolean(),
  profile_picture: z
    .file("Foto profil harus diisi")
    .max(2048 * 2048 * 5, "File terlalu besar")
    .optional(),
});

export type UpdateUserSchemaType = z.infer<typeof updateUserProfileSchema>;
