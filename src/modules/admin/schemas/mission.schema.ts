import { z } from "zod/v4";

export const missionSchema = z.object({
  name: z
    .string("Nama misi harus diisi")
    .min(3, "Nama misi minimal 3 karakter")
    .max(80, "Nama misi maksimal 80 karakter"),
  description: z.string().max(255, "Deskripsi maksimal 255 karakter"),
  mission_type: z.enum(["DAILY", "WEEKLY", "MONTHLY", "SPECIAL", "GOAL"]),
  completion_count: z.coerce.number(),
  xp_reward: z.coerce.number(),
  badge_reward: z.coerce.number().optional(),
  is_active: z.boolean(),
});

export type MissionSchemaType = z.infer<typeof missionSchema>;
