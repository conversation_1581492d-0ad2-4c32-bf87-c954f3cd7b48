import { Suspense } from "react";
import { Plus } from "lucide-react";

import { getGamificationMission } from "../services/get-mission.service";
import { MissionTable } from "../components/mission-gamification/mission-table";

import { LoadingRipple } from "@/components/shared/loading-ripple";
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { MissionForm } from "../components/mission-gamification/mission-form";

export function AdminMissionView() {
  return (
    <Card className="container mx-auto size-full rounded-none border-0 shadow-none md:rounded-xl md:border">
      <CardHeader className="border-b">
        <CardTitle className="clamp-[text,sm,lg] font-bold">Gamification Missions</CardTitle>
        <CardDescription className="max-w-[70ch]">
          <PERSON><PERSON><PERSON> adalah daftar misi yang tersedia dalam sistem. Admin dapat mengelola misi,
          termasuk menambahkan, mengubah, atau menghapus misi sesuai kebutuhan.
        </CardDescription>

        <CardAction>
          <Dialog>
            <DialogTrigger asChild>
              <Button size="sm" variant="outline">
                <Plus />
                Tambah Misi
              </Button>
            </DialogTrigger>

            <DialogContent>
              <DialogHeader>
                <DialogTitle>Tambah Misi</DialogTitle>
              </DialogHeader>
              
              <MissionForm type='create' />
            </DialogContent>
          </Dialog>
        </CardAction>
      </CardHeader>

      <CardContent className="size-full">
        <Suspense
          fallback={
            <div className="flex size-full items-center justify-center">
              <LoadingRipple />
            </div>
          }
        >
          <Mission />
        </Suspense>
      </CardContent>
    </Card>
  );
}

async function Mission() {
  const data = await getGamificationMission();

  return <MissionTable data={data} />;
}
