import { Suspense } from "react";

import { Card } from "@/components/ui/card";
import { LoadingAnimation } from "@/components/shared/loading-animation";

import { QuerySchemaType } from "../schemas/query.schema";
import { getUserProfiles } from "../services/get-user-profiles.service";
import { UserTable } from "../components/user-management/user-table";

export function UserManagementView({
  query,
  isDesktop,
}: {
  query: QuerySchemaType;
  isDesktop: boolean;
}) {
  const promise = getUserProfiles(query);

  return (
    <div className="container mx-auto flex flex-col gap-4 p-0 md:gap-6 md:p-4 md:px-6">
      <Card className="max-w-full rounded-none border-0 pb-0 shadow-none md:rounded-xl md:border">
        <Suspense
          fallback={
            <div className="flex h-[calc(100vh-14rem-1px)] flex-col items-center justify-center gap-4 p-4">
              <LoadingAnimation />
            </div>
          }
        >
          <UserTable promise={promise} isDesktop={isDesktop} />
        </Suspense>
      </Card>
    </div>
  );
}
