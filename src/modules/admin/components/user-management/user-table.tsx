"use client";

import { Fragment, use, useRef, useState } from "react";
import { useSearchParams } from "next/navigation";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table";
import {
  Check,
  ChevronDown,
  ChevronDownIcon,
  ChevronUpIcon,
  Copy,
  EllipsisVertical,
  InfoIcon,
  Mail,
  Pencil,
  Send,
  ShieldUser,
  Trash2,
  UserCheck,
  Users,
  XIcon,
} from "lucide-react";
import { useSession } from "next-auth/react";
import { format } from "date-fns";
import { RiWhatsappLine } from "@remixicon/react";
import { useInView } from "motion/react";

import { QuerySchemaType } from "../../schemas/query.schema";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { IGetUsersResponse, IGetUsersResult } from "@/types/response.type";
import { cn } from "@/utils/cn";
import { Button } from "@/components/ui/button";

import { UserTableFooter } from "./user-table-footer";
import { UserTableHeader } from "./user-table-header";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { UpdateUserAdminForm } from "./update-user.form";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { getImageUrl } from "@/utils/image-url";
import { InfoCardTable } from "./info-card-table";
import { AlertDialog, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { DeleteUserForm } from "./delete-user.form";

export function UserTable({
  promise,
  isDesktop,
}: {
  promise: Promise<IGetUsersResponse | null>;
  isDesktop: boolean;
}) {
  const getUsers = use(promise);
  const { data: session } = useSession();

  const searchParams = useSearchParams();

  const searchLimit = Number(searchParams.get("limit"));
  const searchOffset = Number(searchParams.get("offset"));

  const query: QuerySchemaType = {
    search: searchParams.get("search") || "",
    limit: searchLimit || isDesktop ? 25 : 15,
    offset: searchOffset || 0,
  };

  const indicatorRef = useRef<HTMLTableRowElement>(null);
  const isInView = useInView(indicatorRef, { amount: "some" });

  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    date_of_birth: false,
    created_at: false,
    gender: false,
  });
  const [sorting, setSorting] = useState<SortingState>([{ id: "created_at", desc: false }]);
  const [dialogEdit, setDialogEdit] = useState({
    isOpen: false,
    user: null as IGetUsersResult | null,
  });
  const [dialogDelete, setDialogDelete] = useState({
    isOpen: false,
    user: null as IGetUsersResult | null,
  });

  const columns: ColumnDef<IGetUsersResult>[] = [
    {
      id: "expander",
      header: () => null,
      cell: ({ row }) => {
        return row.getCanExpand() ? (
          <Button
            {...{
              className: "size-7 shadow-none text-muted-foreground",
              onClick: row.getToggleExpandedHandler(),
              "aria-expanded": row.getIsExpanded(),
              "aria-label": row.getIsExpanded()
                ? `Collapse details for ${row.original.full_name}`
                : `Expand details for ${row.original.full_name}`,
              size: "icon",
              variant: "ghost",
            }}
          >
            {row.getIsExpanded() ? (
              <ChevronUpIcon className="opacity-60" size={16} aria-hidden="true" />
            ) : (
              <ChevronDownIcon className="opacity-60" size={16} aria-hidden="true" />
            )}
          </Button>
        ) : null;
      },
    },
    {
      accessorKey: "full_name",
      header: "Nama Lengkap",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2 leading-none">
            {row.original.profile_picture && (
              <Avatar className="sticky left-4">
                <AvatarImage src={getImageUrl(row.original.profile_picture)} />
                <AvatarFallback>{row.original.full_name?.charAt(0) || ""}</AvatarFallback>
              </Avatar>
            )}
            {row.original.username === session?.user.username && <Badge>You</Badge>}
            {row.original.full_name || <span className="text-muted-foreground text-xs">N/A</span>}
          </div>
        );
      },
    },
    {
      accessorKey: "username",
      header: "Username",
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "gender",
      header: "Jenis Kelamin",
      cell: ({ row }) => (
        <span
          className={cn("font-medium", {
            "text-blue-500": row.original.gender === "Laki-laki",
            "text-pink-500": row.original.gender === "Perempuan",
          })}
        >
          {row.original.gender || <span className="text-muted-foreground text-xs">N/A</span>}
        </span>
      ),
    },
    {
      accessorKey: "created_at",
      header: "Tanggal Daftar",
      cell: ({ row }) => format(new Date(row.original.created_at), "dd MMM yyyy"),
    },
    {
      accessorKey: "date_of_birth",
      header: "Tanggal Lahir",
      cell: ({ row }) => format(new Date(row.original.date_of_birth), "dd MMM yyyy"),
    },
    {
      accessorKey: "phone_number",
      header: "Nomor Telepon",
      cell: ({ row }) => (
        <div className="flex items-center gap-2 leading-none">
          {row.original.phone_number || <span className="text-muted-foreground text-xs">N/A</span>}
        </div>
      ),
    },
    {
      id: "status",
      header: "Status",
      cell: ({ row }) => {
        return (
          <div className="grid gap-1">
            {row.original.is_staff && <Badge variant="success">Admin</Badge>}
            {row.original.is_google && <Badge variant="secondary">Google</Badge>}

            <Badge variant={row.original.is_active ? "default" : "destructive"}>
              {row.original.is_active ? "Active" : "Inactive"}
            </Badge>
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        const handleOpenDialogEdit = () => {
          setDialogEdit({ isOpen: true, user: row.original });
        };

        const handleOpenDialogDelete = () => {
          setDialogDelete({ isOpen: true, user: row.original });
        };

        return (
          <div className="flex items-center justify-center gap-2 leading-none">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="icon" variant="ghost" className="h-fit w-fit">
                  <EllipsisVertical size={16} aria-hidden="true" className="opacity-60" />
                </Button>
              </DropdownMenuTrigger>

              <DropdownMenuContent align="end" className="w-40">
                <DropdownMenuGroup>
                  <DropdownMenuLabel>Contact</DropdownMenuLabel>
                  <DropdownMenuItem
                    onClick={() => {
                      window.open(
                        `mailto:${row.original.email}?subject=Hello%20from%20Komunitas%20Koding`,
                        "_blank",
                      );
                    }}
                  >
                    <Mail />
                    Email
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => {
                      const phone = row.original.phone_number.split(" ").join("").replace("+", "");
                      window.open(
                        `https://wa.me/${phone}?text=Hello%20from%20Komunitas%20Koding`,
                        "_blank",
                      );
                    }}
                  >
                    <RiWhatsappLine />
                    Whatsapp
                  </DropdownMenuItem>
                </DropdownMenuGroup>
                <DropdownMenuSeparator />
                <DropdownMenuGroup>
                  <DropdownMenuLabel>Management</DropdownMenuLabel>
                  <DropdownMenuItem
                    variant="default"
                    onClick={() => {
                      navigator.clipboard.writeText(
                        `ID: ${row.original.id}\nName: ${row.original.full_name}\nEmail: ${row.original.email}\nPhone: ${row.original.phone_number}\nBio: ${row.original.bio || "-"}`,
                      );
                    }}
                  >
                    <Copy />
                    Copy
                  </DropdownMenuItem>
                  <DropdownMenuItem variant="default" onClick={handleOpenDialogEdit}>
                    <Pencil />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem variant="destructive" onClick={handleOpenDialogDelete}>
                    <Trash2 />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: getUsers?.results || [],
    columns,
    getRowCanExpand: (row) => Boolean(row.original.bio),
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onSortingChange: setSorting,
    enableSortingRemoval: false,
    state: { sorting, columnVisibility },
  });

  const handleCloseDialogEdit = (open: boolean) => {
    if (!open) {
      setDialogEdit((prev) => ({ ...prev, isOpen: false }));
    }
  };

  const handleCloseDialogDelete = (open: boolean) => {
    if (!open) {
      setDialogDelete({ isOpen: false, user: null });
    }
  };

  return (
    <div className="flex flex-col px-6">
      <InfoCardTable count={getUsers?.count || 0} users={getUsers?.results || []} />

      <UserTableHeader table={table} />

      <div className="relative flex max-h-[calc(100vh-21rem-10px)] flex-col gap-4 overflow-hidden rounded-md border">
        <Table>
          <TableHeader className="bg-background/90 sticky top-0 z-10 backdrop-blur-xs">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="hover:bg-transparent">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="h-11">
                      {header.isPlaceholder ? null : header.column.getCanSort() ? (
                        <div
                          className={cn(
                            header.column.getCanSort() &&
                              "flex h-full cursor-pointer items-center justify-between gap-2 select-none",
                          )}
                          onClick={header.column.getToggleSortingHandler()}
                          tabIndex={header.column.getCanSort() ? 0 : undefined}
                        >
                          {flexRender(header.column.columnDef.header, header.getContext())}
                          {{
                            asc: (
                              <ChevronUpIcon
                                className="shrink-0 opacity-60"
                                size={16}
                                aria-hidden="true"
                              />
                            ),
                            desc: (
                              <ChevronDownIcon
                                className="shrink-0 opacity-60"
                                size={16}
                                aria-hidden="true"
                              />
                            ),
                          }[header.column.getIsSorted() as string] ?? null}
                        </div>
                      ) : (
                        flexRender(header.column.columnDef.header, header.getContext())
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, idx, arr) => (
                <Fragment key={row.id}>
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                    className={cn(idx === arr.length - 1 ? "!border-b-0" : "")}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell
                        key={cell.id}
                        className="whitespace-nowrap [&:has([aria-expanded])]:w-px [&:has([aria-expanded])]:py-0 [&:has([aria-expanded])]:pr-0"
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>

                  {row.getIsExpanded() && (
                    <TableRow>
                      <TableCell
                        colSpan={row.getVisibleCells().length}
                        className="via-muted bg-gradient-to-r from-transparent to-transparent whitespace-normal"
                      >
                        <div className="text-muted-foreground flex shrink items-start py-2">
                          <span
                            className="me-3 mt-0.5 flex w-7 shrink-0 justify-center"
                            aria-hidden="true"
                          >
                            <InfoIcon className="opacity-60" size={16} />
                          </span>
                          <p className="max-w-[60ch] text-sm">
                            <span className="text-foreground font-medium">Bio: </span>
                            {row.original.bio}
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </Fragment>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}

            <tr ref={indicatorRef} className="!border-b-0">
              <td colSpan={columns.length} className="!border-b-0"></td>
            </tr>
          </TableBody>
        </Table>

        <Button
          type="button"
          size="icon"
          variant="outline"
          className={cn(
            "absolute bottom-6 left-1/2 z-50 -translate-x-1/2 rounded-full",
            isInView ? "hidden" : "",
          )}
          onClick={() => {
            if (indicatorRef.current) {
              indicatorRef.current.scrollIntoView({ behavior: "smooth" });
            }
          }}
        >
          <ChevronDown />
        </Button>
      </div>

      <UserTableFooter isDesktop={isDesktop} users={getUsers!} />

      <Dialog open={dialogEdit.isOpen} onOpenChange={handleCloseDialogEdit}>
        <DialogContent className="md:max-w-xl">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>Edit user information.</DialogDescription>
          </DialogHeader>

          <UpdateUserAdminForm user={dialogEdit.user!} />
        </DialogContent>
      </Dialog>

      <AlertDialog open={dialogDelete.isOpen} onOpenChange={handleCloseDialogDelete}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Apakah Anda yakin ingin menghapus {dialogDelete.user?.full_name} ini?</AlertDialogTitle>
              <AlertDialogDescription>
                User ini akan dihapus secara permanen. Tindakan ini tidak dapat dibatalkan.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <DeleteUserForm user={dialogDelete.user!} />
            </AlertDialogFooter>
          </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
