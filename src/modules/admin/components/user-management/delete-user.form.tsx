import { AlertDialogAction } from "@/components/ui/alert-dialog";
import { IGetUsersResult } from "@/types/response.type";

export function DeleteUserForm({ user }: { user: IGetUsersResult }) {
  return (
    <form>
      <input type="hidden" name="id" value={user?.id} />
      <AlertDialogAction type="submit" className="bg-gradient-purple">
        Hapus
      </AlertDialogAction>
    </form>
  );
}
