import Image from "next/image";
import Link from "next/link";
import { format } from "date-fns";
import { toast } from "sonner";
import { startTransition, useActionState, useEffect } from "react";
import { Loader2 } from "lucide-react";
import { useSession } from "next-auth/react";

import { updateAdminUserProfile } from "../../services/update-user-profiles.service";

import { Label, LabelWrapperInput } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { InputPhoneNumber } from "@/components/ui/phone-number";
import { DatePicker } from "@/components/ui/date-picker";
import { TextAreaCount } from "@/components/ui/text-area-count";
import { InputAvatar } from "@/components/ui/input-avatar";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";

import { initialActionState } from "@/types/action.type";
import { IGetUsersResult } from "@/types/response.type";
import { parsePhoneNumber } from "@/utils/helper";
import { getImageUrl } from "@/utils/image-url";
import { cn } from "@/utils/cn";

export function UpdateUserAdminForm({ user }: { user: IGetUsersResult }) {
  const { data: session } = useSession();

  const [state, action, isPending] = useActionState(updateAdminUserProfile, initialActionState);

  const errorObj = typeof state.errors === "object" ? state.errors : null;

  useEffect(() => {
    if (state.success && state.message) {
      toast.success(state.message);
    }

    if (!state.success && state.message) {
      toast.error(state.message);
      console.log(state.errors);
    }
  }, [state.success, state.message, state.errors]);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    const checkFields = () => {
      const payload = Object.fromEntries(formData.entries());
      const extract = Object.entries(payload).filter(([key, value]) => {
        switch (key) {
          case "profile_picture":
            return (value as File).size > 0;
          case "date_of_birth":
            return user.date_of_birth !== format(new Date(value as string), "yyyy-MM-dd");
          default:
            return user[key as keyof typeof user] !== value;
        }
      });

      return extract.length > 0;
    };

    const isChanged = checkFields();

    if (!isChanged) {
      toast.warning("Tidak ada perubahan", {
        description: "Silakan ubah data terlebih dahulu.",
      });
      return;
    }

    if (user.is_google) {
      formData.append("username", user.username);
      formData.append("email", user.email);
    }

    startTransition(() => {
      action(formData);
    });
  };

  if (user.username === session?.user.username) {
    return (
      <div className="flex flex-col gap-4">
        <p>Anda tidak dapat mengubah profil Anda sendiri.</p>
        <Button asChild>
          <Link href="/dashboard/setting">Buka Profile Saya</Link>
        </Button>
      </div>
    );
  }

  return (
    <form
      onSubmit={handleSubmit}
      className="no-scrollbar flex max-h-[calc(100dvh-1rem)] w-full flex-col gap-0 overflow-y-auto md:gap-4"
    >
      <input type="text" defaultValue={user.id} name="id" className="sr-only" />
      {/* User Profile Picture */}
      <div className="mb-4 flex border-separate items-center justify-center rounded-xl border p-4 md:mb-0">
        <div className="relative">
          {user.profile_picture && (
            <Avatar className="pointer-events-none absolute z-50 size-24">
              {user?.profile_picture && (
                <Image
                  src={getImageUrl(user?.profile_picture)}
                  alt={user?.username || ""}
                  width={48}
                  height={48}
                  className="absolute size-full object-cover"
                />
              )}
              <AvatarImage
                src={getImageUrl(user?.profile_picture || "")}
                alt={user?.username || ""}
                width={48}
                height={48}
                className="object-cover"
              />
              <AvatarFallback className="bg-gradient-purple text-lg font-medium text-white">
                {user?.full_name?.charAt(0) || ""}
              </AvatarFallback>
            </Avatar>
          )}

          <InputAvatar
            name="profile_picture"
            disabled={isPending}
            maxSize={2}
            className="mx-auto size-24 shrink-0"
            initialFiles={
              state.inputs?.profile_picture
                ? [
                    {
                      id: state.inputs?.profile_picture.name,
                      name: state.inputs?.profile_picture.name,
                      size: state.inputs?.profile_picture.size,
                      type: state.inputs?.profile_picture.type,
                      url: URL.createObjectURL(state.inputs?.profile_picture),
                    },
                  ]
                : undefined
            }
          />
        </div>
      </div>

      {/* User Profile */}
      <div className="mb-4 flex w-full flex-col gap-4 md:mb-0 md:flex-row md:gap-2">
        <LabelWrapperInput
          label="Nama Lengkap"
          htmlFor="full_name"
          className="w-full"
          errors={errorObj?.full_name}
        >
          <Input
            type="text"
            name="full_name"
            defaultValue={user.full_name || undefined}
            required
            disabled={isPending}
            placeholder="Nama Lengkap"
          />
        </LabelWrapperInput>

        <LabelWrapperInput
          label="Username"
          htmlFor="username"
          className="w-full"
          errors={errorObj?.username}
        >
          <Input
            type="text"
            name="username"
            defaultValue={user.username}
            required
            disabled={isPending || user.is_google}
            placeholder="Username"
          />
        </LabelWrapperInput>
      </div>

      <div className="flex w-full flex-col gap-4 md:flex-row md:gap-2">
        <LabelWrapperInput
          label="Email"
          htmlFor="email"
          className="w-full"
          errors={errorObj?.email}
        >
          <Input
            type="email"
            name="email"
            defaultValue={user.email}
            disabled={isPending || user.is_google}
            required
            placeholder="Email"
          />
        </LabelWrapperInput>

        <LabelWrapperInput
          label="Jenis Kelamin"
          htmlFor="gender"
          className="w-full"
          errors={errorObj?.gender}
        >
          <Select name="gender" required defaultValue={user.gender} disabled={isPending}>
            <SelectTrigger className="w-full">
              <SelectValue defaultValue={user.gender} placeholder="Jenis Kelamin" />
            </SelectTrigger>

            <SelectContent>
              <SelectItem value="Laki-laki">Laki-laki</SelectItem>
              <SelectItem value="Perempuan">Perempuan</SelectItem>
            </SelectContent>
          </Select>
        </LabelWrapperInput>
      </div>

      <div className="mb-4 flex w-full flex-col gap-4 md:mb-0 md:flex-row md:gap-2">
        <LabelWrapperInput
          label="Nomor Telepon"
          htmlFor="phone_number"
          className="w-full"
          errors={errorObj?.phone_number}
        >
          <InputPhoneNumber
            name="phone_number"
            value={parsePhoneNumber(user.phone_number) || undefined}
            required
            disabled={isPending}
            placeholder="Nomor Telepon"
          />
        </LabelWrapperInput>

        <LabelWrapperInput
          label="Tanggal Lahir"
          htmlFor="date_of_birth"
          className="w-full"
          errors={errorObj?.date_of_birth}
        >
          <DatePicker
            id="date_of_birth"
            placeholder="Tanggal Lahir"
            required
            disabledButton={isPending}
            value={user.date_of_birth ? new Date(user.date_of_birth) : undefined}
            disabled={[{ after: new Date() }]}
          />
        </LabelWrapperInput>
      </div>

      <LabelWrapperInput label="Biografi" htmlFor="bio" className="w-full" errors={errorObj?.bio}>
        <TextAreaCount
          name="bio"
          initialValue={user.bio || undefined}
          disabled={isPending}
          placeholder="Masukan hal tentang diri Anda"
          maxLength={250}
          rows={4}
        />
      </LabelWrapperInput>

      {/* User Role */}
      <div className="border-input has-data-[state=checked]:border-primary/50 relative flex w-full items-start gap-2 rounded-md border p-4 shadow-xs outline-none">
        <Switch
          name="is_staff"
          className="group order-1 h-4 w-6 after:absolute after:inset-0 [&_span]:size-3 data-[state=checked]:[&_span]:translate-x-2 data-[state=checked]:[&_span]:rtl:-translate-x-2"
          disabled={isPending || user.is_google}
          defaultChecked={user.is_staff}
        />
        <div className="grid grow gap-2">
          <Label htmlFor="is_staff">
            Admin{" "}
            <span className="text-muted-foreground text-xs leading-[inherit] font-normal">
              (Optional)
            </span>
          </Label>
          <p className="text-muted-foreground text-xs">
            Fitur ini digunakan untuk mengubah pengguna menjadi admin.
          </p>
        </div>
      </div>

      <Button type="submit" disabled={isPending} className="relative w-full">
        <Loader2
          className={cn(
            "absolute top-1/2 left-4 -translate-y-1/2 animate-spin opacity-0 transition-opacity duration-300",
            {
              "opacity-100": isPending,
            },
          )}
        />
        Simpan Perubahan
      </Button>
    </form>
  );
}
