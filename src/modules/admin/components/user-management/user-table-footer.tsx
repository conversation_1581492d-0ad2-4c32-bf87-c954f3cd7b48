import qs from "qs";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { ChevronLeft, ChevronRightIcon, ChevronsLeft, ChevronsRight } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { IGetUsersResponse } from "@/types/response.type";

export function UserTableFooter({
  isDesktop,
  users,
}: {
  isDesktop: boolean;
  users: IGetUsersResponse;
}) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const search = searchParams.get("search");
  const searchLimit = Number(searchParams.get("limit") || (isDesktop ? 25 : 15));
  const searchOffset = Number(searchParams.get("offset") || 0);

  const [limit, setLimit] = useState(searchLimit);

  const handleSelectChange = (value: string) => {
    setLimit(Number(value));
    const newQuery = { limit: Number(value) };
    const queryString = qs.stringify(newQuery);
    router.push(`/dashboard/admin/user-management?${queryString}`);
  };

  return (
    <div className="flex items-start justify-between py-4 md:items-center">
      <div className="flex items-center gap-2">
        <Select defaultValue={limit.toString()} onValueChange={handleSelectChange}>
          <SelectTrigger size="sm" className="w-[100px] data-[size=sm]:h-7">
            <SelectValue placeholder={isDesktop ? "25" : "15"} className="text-sm" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="15">15</SelectItem>
            <SelectItem value="25">25</SelectItem>
            <SelectItem value="50">50</SelectItem>
            <SelectItem value="100">100</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <p className="text-muted-foreground hidden text-xs sm:block">
        Menampilkan {limit} dari {users.count} Pengguna
      </p>

      <div className="flex flex-col items-end gap-4">
        <div className="flex items-center gap-2">
          <Button
            size="icon"
            variant="outline"
            disabled={!users.previous}
            asChild={users.previous != null}
          >
            <Link
              href={{
                pathname: "/dashboard/admin/user-management",
                query: { search, offset: 0, limit: searchLimit },
              }}
            >
              <ChevronsLeft />
            </Link>
          </Button>
          <Button
            size="icon"
            variant="outline"
            disabled={!users.previous}
            asChild={users.previous != null}
          >
            <Link
              href={{
                pathname: "/dashboard/admin/user-management",
                query: {
                  search,
                  offset: Math.max((searchOffset as number) - searchLimit, 0),
                  limit: searchLimit,
                },
              }}
            >
              <ChevronLeft />
            </Link>
          </Button>
          <Button size="icon" variant="outline" disabled={!users.next} asChild={users.next != null}>
            <Link
              href={{
                pathname: "/dashboard/admin/user-management",
                query: {
                  search,
                  offset: Math.min((searchOffset as number) + searchLimit, users.count || 0),
                  limit: searchLimit,
                },
              }}
            >
              <ChevronRightIcon />
            </Link>
          </Button>
          <Button size="icon" variant="outline" disabled={!users.next} asChild={users.next != null}>
            <Link
              href={{
                pathname: "/dashboard/admin/user-management",
                query: { search, offset: users.count - searchLimit || 0, limit: searchLimit },
              }}
            >
              <ChevronsRight />
            </Link>
          </Button>
        </div>
        <p className="text-muted-foreground block text-xs sm:hidden">
          Menampilkan {limit} dari {users.count} Pengguna
        </p>
      </div>
    </div>
  );
}
