import { IGetUsersResult } from "@/types/response.type";
import { Ri<PERSON><PERSON><PERSON><PERSON>ill, RiLinkedinBoxFill } from "@remixicon/react";
import { ShieldUser, UserCheck, Users } from "lucide-react";

export function InfoCardTable({ count, users }: { count: number; users: IGetUsersResult[] }) {
  const totalActive = users?.filter((user) => user.is_active).length;
  const totalAdmin = users?.filter((user) => user.is_staff).length;
  const totalGoogleUser = users?.filter((user) => user.is_google).length;

  return (
    <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
      <div className="rounded-xl border p-4">
        <div className="flex h-full items-center gap-2">
          <div className="flex h-full w-full flex-col justify-between gap-1">
            <div className="flex w-full items-center justify-between">
              <p className="text-sm font-medium">Total User</p>
              <Users className="size-4" />
            </div>
            <p className="text-2xl font-bold">{count}</p>
          </div>
        </div>
      </div>
      <div className="rounded-xl border p-4">
        <div className="flex h-full items-center gap-2">
          <div className="flex h-full w-full flex-col justify-between gap-1">
            <div className="flex w-full items-center justify-between">
              <p className="text-sm font-medium">Active User</p>
              <UserCheck className="size-4" />
            </div>
            <p className="text-2xl font-bold">{totalActive}</p>
          </div>
        </div>
      </div>
      <div className="rounded-xl border p-4">
        <div className="flex h-full items-center gap-2">
          <div className="flex h-full w-full flex-col justify-between gap-1">
            <div className="flex w-full items-center justify-between">
              <p className="text-sm font-medium">Admins</p>
              <ShieldUser className="size-4" />
            </div>
            <p className="text-2xl font-bold">{totalAdmin}</p>
          </div>
        </div>
      </div>
      <div className="rounded-xl border p-4">
        <div className="flex h-full items-center gap-2">
          <div className="flex h-full w-full flex-col justify-between gap-1">
            <div className="flex w-full items-center justify-between">
              <p className="text-sm font-medium">Socials</p>
              <div className="flex items-center gap-2">
                <RiGoogleFill className="size-4" />
                <RiLinkedinBoxFill className="size-4" />
              </div>
            </div>

            <div className="flex gap-4 self-end">
              <div className="flex items-end gap-2">
                <p className="text-xs">Google</p>
                <p className="text-2xl leading-none font-bold">{totalGoogleUser}</p>
              </div>
              <div className="flex items-end gap-2">
                <p className="text-xs">Linkedin</p>
                <p className="text-2xl leading-none font-bold">{"0"}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
