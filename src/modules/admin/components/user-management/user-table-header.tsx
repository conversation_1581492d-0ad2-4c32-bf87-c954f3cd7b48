import qs from "qs";
import { useEffect } from "react";
import { Eye, Plus } from "lucide-react";
import { useSearchParams, useRouter } from "next/navigation";
import { Table } from "@tanstack/react-table";

import { InputSearch } from "@/components/ui/input-search";
import { useDebounce } from "@/hooks/use-debounce";
import { Button } from "@/components/ui/button";

import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { IGetUsersResult } from "@/types/response.type";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { CreateUserAdminForm } from "./create-user.form";

export function UserTableHeader({ table }: { table: Table<IGetUsersResult> }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const searchLimit = Number(searchParams.get("limit"));

  const [debounceSearch, setSearch, search] = useDebounce(searchParams.get("search") || "", 300);

  useEffect(() => {
    if (debounceSearch !== searchParams.get("search")) {
      const newQuery = {
        ...(searchLimit && { limit: searchLimit }),
        search: debounceSearch,
      };
      const queryString = qs.stringify(newQuery);
      router.push(`/dashboard/admin/user-management?${queryString}`);
    }
  }, [debounceSearch]);

  const filterableColumns = table.getAllColumns().filter((column) => column.getCanFilter());

  return (
    <div className="flex justify-between gap-4 pb-4">
      <div className="flex flex-col gap-2 sm:flex-row">
        <InputSearch
          name="search"
          placeholder="Cari user berdasarkan nama, username, email atau nomor telepon"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size="icon" variant="outline" className="border-border shadow-none">
              <Eye />
            </Button>
          </DropdownMenuTrigger>

          <DropdownMenuContent align="end">
            <DropdownMenuLabel className="text-muted-foreground text-xs font-medium">
              Filter
            </DropdownMenuLabel>

            <DropdownMenuSeparator />

            {filterableColumns.map((column) => (
              <DropdownMenuCheckboxItem
                key={column.id}
                className="capitalize"
                checked={column.getIsVisible()}
                onCheckedChange={(value) => column.toggleVisibility(!!value)}
              >
                {column.columnDef.header?.toString()}
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Dialog>
        <DialogTrigger asChild>
          <Button>
            <Plus />
            Tambah User
          </Button>
        </DialogTrigger>

        <DialogContent>
          <DialogHeader>
            <DialogTitle>Tambah User</DialogTitle>
            <DialogDescription className="text-balance">
              Formulir ini digunakan untuk menambahkan user baru ke dalam sistem.
            </DialogDescription>
          </DialogHeader>
          <CreateUserAdminForm />
        </DialogContent>
      </Dialog>
    </div>
  );
}
