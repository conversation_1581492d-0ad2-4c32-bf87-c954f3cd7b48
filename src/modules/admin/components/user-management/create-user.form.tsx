import { useSession } from "next-auth/react";
import { useActionState, useEffect } from "react";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

import { createAdminUserProfile } from "../../services/create-user-profiles.service";

import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { InputPhoneNumber } from "@/components/ui/phone-number";
import { DatePicker } from "@/components/ui/date-picker";
import { TextAreaCount } from "@/components/ui/text-area-count";
import { InputAvatar } from "@/components/ui/input-avatar";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label, LabelWrapperInput } from "@/components/ui/label";

import { cn } from "@/utils/cn";

import { initialActionState } from "@/types/action.type";

export function CreateUserAdminForm() {
  const [state, action, isPending] = useActionState(createAdminUserProfile, initialActionState);

  useEffect(() => {
    if (state.success && state.message) {
      toast.success(state.message);
    }

    if (!state.success && state.message) {
      toast.error(state.message);
      console.log(state.errors);
    }
  }, [state.success, state.message, state.errors]);

  return (
    <form
      action={action}
      className="no-scrollbar flex max-h-[calc(100dvh-1rem)] w-full flex-col gap-0 overflow-y-auto md:gap-4"
    >
      {/* User Profile Picture */}
      <div className="mb-4 flex border-separate items-center justify-center rounded-xl border p-4 md:mb-0">
        <div className="relative">
          <InputAvatar
            name="profile_picture"
            disabled={isPending}
            maxSize={2}
            className="mx-auto size-24 shrink-0"
            initialFiles={
              state.inputs?.profile_picture
                ? [
                    {
                      id: state.inputs?.profile_picture.name,
                      name: state.inputs?.profile_picture.name,
                      size: state.inputs?.profile_picture.size,
                      type: state.inputs?.profile_picture.type,
                      url: URL.createObjectURL(state.inputs?.profile_picture),
                    },
                  ]
                : undefined
            }
          />
        </div>
      </div>

      {/* User Profile */}
      <div className="mb-4 flex w-full flex-col gap-4 md:mb-0 md:flex-row md:gap-2">
        <LabelWrapperInput label="Nama Lengkap" htmlFor="full_name" className="w-full">
          <Input
            type="text"
            name="full_name"
            required
            disabled={isPending}
            defaultValue={state.inputs?.full_name}
            placeholder="Nama Lengkap"
          />
        </LabelWrapperInput>

        <LabelWrapperInput label="Username" htmlFor="username" className="w-full">
          <Input
            type="text"
            name="username"
            required
            disabled={isPending}
            defaultValue={state.inputs?.username}
            placeholder="Username"
          />
        </LabelWrapperInput>
      </div>

      <div className="flex w-full flex-col gap-4 md:flex-row md:gap-2">
        <LabelWrapperInput label="Email" htmlFor="email" className="w-full">
          <Input
            type="email"
            name="email"
            disabled={isPending}
            defaultValue={state.inputs?.email}
            required
            placeholder="Email"
          />
        </LabelWrapperInput>

        <LabelWrapperInput label="Jenis Kelamin" htmlFor="gender" className="w-full">
          <Select name="gender" required disabled={isPending}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Jenis Kelamin" />
            </SelectTrigger>

            <SelectContent>
              <SelectItem value="Laki-laki">Laki-laki</SelectItem>
              <SelectItem value="Perempuan">Perempuan</SelectItem>
            </SelectContent>
          </Select>
        </LabelWrapperInput>
      </div>

      <div className="mb-4 flex w-full flex-col gap-4 md:mb-0 md:flex-row md:gap-2">
        <LabelWrapperInput label="Nomor Telepon" htmlFor="phone_number" className="w-full">
          <InputPhoneNumber
            name="phone_number"
            required
            disabled={isPending}
            placeholder="Nomor Telepon"
          />
        </LabelWrapperInput>

        <LabelWrapperInput label="Tanggal Lahir" htmlFor="date_of_birth" className="w-full">
          <DatePicker
            id="date_of_birth"
            placeholder="Tanggal Lahir"
            required
            disabledButton={isPending}
            disabled={[{ after: new Date() }]}
          />
        </LabelWrapperInput>
      </div>

      <LabelWrapperInput label="Biografi" htmlFor="bio" className="w-full">
        <TextAreaCount
          name="bio"
          disabled={isPending}
          placeholder="Masukan hal tentang diri Anda"
          maxLength={250}
          rows={4}
        />
      </LabelWrapperInput>

      {/* User Role */}
      <div className="border-input has-data-[state=checked]:border-primary/50 relative flex w-full items-start gap-2 rounded-md border p-4 shadow-xs outline-none">
        <Switch
          name="is_staff"
          className="order-1 h-4 w-6 after:absolute after:inset-0 [&_span]:size-3 data-[state=checked]:[&_span]:translate-x-2 data-[state=checked]:[&_span]:rtl:-translate-x-2"
        />
        <div className="grid grow gap-2">
          <Label htmlFor="is_staff">
            Admin{" "}
            <span className="text-muted-foreground text-xs leading-[inherit] font-normal">
              (Optional)
            </span>
          </Label>
          <p className="text-muted-foreground text-xs">
            Fitur ini digunakan untuk mengubah pengguna menjadi admin.
          </p>
        </div>
      </div>

      <Button type="submit" disabled={isPending} className="relative w-full">
        <Loader2
          className={cn(
            "absolute top-1/2 left-4 -translate-y-1/2 animate-spin opacity-0 transition-opacity duration-300",
            {
              "opacity-100": isPending,
            },
          )}
        />
        Simpan Perubahan
      </Button>
    </form>
  );
}
