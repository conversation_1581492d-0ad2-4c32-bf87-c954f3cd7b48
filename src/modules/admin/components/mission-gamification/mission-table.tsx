"use client";

import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { useEffect, useRef, useState } from "react";

import { Checkbox } from "@/components/ui/checkbox";
import { GamificationMission } from "@/types/response.type";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Calendar,
  ChevronDownIcon,
  ChevronUpIcon,
  Edit,
  SearchIcon,
  Star,
  Target,
  Trophy,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/utils/cn";
import { parseNum } from "@/utils/helper";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";

const getMissionTypeBadgeColor = (type: GamificationMission["mission_type"]) => {
  switch (type) {
    case "GOAL":
      return "bg-blue-100 text-blue-800";
    case "SPECIAL":
      return "bg-yellow-100 text-yellow-800";
    case "DAILY":
      return "bg-green-100 text-green-800";
    case "WEEKLY":
      return "bg-purple-100 text-purple-800";
    case "MONTHLY":
      return "bg-red-100 text-red-800";
  }
};

const getMissionTypeIcon = (type: GamificationMission["mission_type"]) => {
  switch (type) {
    case "GOAL":
      return <Target className="h-4 w-4" />;
    case "DAILY":
      return <Calendar className="h-4 w-4" />;
    case "WEEKLY":
      return <Trophy className="h-4 w-4" />;
    case "MONTHLY":
      return <Trophy className="h-4 w-4" />;
    case "SPECIAL":
      return <Star className="h-4 w-4" />;
  }
};

export function MissionTable({ data }: { data: Array<GamificationMission> | null }) {
  const inputRef = useRef<HTMLInputElement>(null);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [rowSelection, setRowSelection] = useState({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  const columns: ColumnDef<GamificationMission>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <div className="flex items-center">
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() ||
              (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Select all"
          />
        </div>
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
    },
    {
      accessorKey: "name",
      header: "Nama Misi",
      cell: ({ row }) => (
        <div className="space-y-0.5">
          <div className="leading-none font-medium">{row.original.name}</div>
          <div className="text-muted-foreground line-clamp-1 max-w-[60ch] text-xs">
            {row.original.description}
          </div>
        </div>
      ),
    },
    {
      accessorKey: "mission_type",
      header: "Tipe Misi",
      cell: ({ row }) => (
        <Badge
          className={cn(
            "flex w-fit items-center gap-1",
            getMissionTypeBadgeColor(row.original.mission_type),
          )}
        >
          {getMissionTypeIcon(row.original.mission_type)}
          {row.original.mission_type}
        </Badge>
      ),
    },
    {
      accessorKey: "xp_reward",
      header: "XP Reward",
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <span className="font-medium">{parseNum(row.original.xp_reward)}</span>
          <span className="text-muted-foreground text-xs">XP</span>
        </div>
      ),
    },
    {
      accessorKey: "badge_reward",
      header: "Badge",
      cell: ({ row }) =>
        row.original.badge_reward ? (
          <Badge variant="secondary">ID: {row.original.badge_reward}</Badge>
        ) : (
          <span className="text-muted-foreground">-</span>
        ),
    },
    {
      accessorKey: "is_active",
      header: "Status",
      cell: ({ row }) => (
        <Badge variant={row.original.is_active ? "black" : "destructive"}>
          {row.original.is_active ? "Active" : "Inactive"}
        </Badge>
      ),
    },
    {
      id: "actions",
      header: "Action",
      size: 60,
      cell: () => (
        <div className="flex items-center justify-center">
          <Button variant="outline" size="icon" className="shadow-none">
            <Edit />
          </Button>
        </div>
      ),
    },
  ];

  const table = useReactTable({
    data: data || [],
    columns,
    state: { sorting, rowSelection, columnFilters },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onRowSelectionChange: setRowSelection,
    onColumnFiltersChange: setColumnFilters,
    enableSortingRemoval: true,
  });

  const name = table.getColumn("name");
  const nameHeader = typeof name?.columnDef.header === "string" ? name.columnDef.header : "";

  useEffect(() => {
    const handleKeydown = (e: KeyboardEvent) => {
      if (e.metaKey && e.key === "k") {
        e.preventDefault();
        inputRef.current?.focus();
      }

      if (e.ctrlKey && e.key === "k") {
        e.preventDefault();
        inputRef.current?.focus();
      }
    };

    window.addEventListener("keydown", handleKeydown);

    return () => {
      window.removeEventListener("keydown", handleKeydown);
    };
  }, []);

  return (
    <div className="space-y-4">
      <div className="">
        <div className="max-w-sm space-y-2">
          <Label htmlFor="name">Cari {nameHeader}</Label>
          <div className="relative">
            <Input
              ref={inputRef}
              id="name"
              type="text"
              placeholder={`Search ${nameHeader}`}
              className="peer ps-9 shadow-none"
              value={(name?.getFilterValue() ?? "") as string}
              onChange={(e) => name?.setFilterValue(e.target.value)}
            />
            <div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50">
              <SearchIcon size={16} />
            </div>
          </div>
        </div>
      </div>

      <div className="bg-muted/10 rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="hover:bg-transparent">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className="relative h-10 select-none"
                      aria-sort={
                        header.column.getIsSorted() === "asc"
                          ? "ascending"
                          : header.column.getIsSorted() === "desc"
                            ? "descending"
                            : "none"
                      }
                      style={{
                        ...(header.id === "actions" && { width: header.getSize() }),
                      }}
                    >
                      {header.isPlaceholder ? null : header.column.getCanSort() ? (
                        <div
                          className={cn(
                            header.column.getCanSort() &&
                              "flex h-full cursor-pointer items-center justify-between gap-2 select-none",
                          )}
                          onClick={header.column.getToggleSortingHandler()}
                          onKeyDown={(e) => {
                            // Enhanced keyboard handling for sorting
                            if (
                              header.column.getCanSort() &&
                              (e.key === "Enter" || e.key === " ")
                            ) {
                              e.preventDefault();
                              header.column.getToggleSortingHandler()?.(e);
                            }
                          }}
                          tabIndex={header.column.getCanSort() ? 0 : undefined}
                        >
                          {flexRender(header.column.columnDef.header, header.getContext())}
                          {{
                            asc: (
                              <ChevronUpIcon
                                className="shrink-0 opacity-60"
                                size={16}
                                aria-hidden="true"
                              />
                            ),
                            desc: (
                              <ChevronDownIcon
                                className="shrink-0 opacity-60"
                                size={16}
                                aria-hidden="true"
                              />
                            ),
                          }[header.column.getIsSorted() as string] ?? (
                            <span className="size-4" aria-hidden="true" />
                          )}
                        </div>
                      ) : (
                        flexRender(header.column.columnDef.header, header.getContext())
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
