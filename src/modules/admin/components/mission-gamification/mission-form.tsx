"use client"

import { useActionState } from "react";

import { missionAction } from "../../services/mission-action.service";

import { GamificationMission } from "@/types/response.type";
import { initialActionState } from "@/types/action.type";

export function MissionForm({
  type,
  mission,
}: {
  type: "create" | "edit";
  mission?: GamificationMission;
}) {
  const [state, action, pending] = useActionState(missionAction, initialActionState);

  return <form>Mission Form</form>;
}
