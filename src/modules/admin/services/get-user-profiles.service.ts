import qs from "qs";

import { QuerySchemaType } from "../schemas/query.schema";

import { tryCatch } from "@/utils/try-catch";
import { apiServer } from "@/lib/api-server";
import { IGetUsersResponse } from "@/types/response.type";

export async function getUserProfiles({ search, offset = 0, limit = 15 }: QuerySchemaType) {
  const queryString = qs.stringify({
    // ordering: "-created_at",
    offset,
    limit,
    search,
  });

  const [res, err] = await tryCatch(
    apiServer({
      method: "GET",
      url: `/api/admin/search-user-profiles?${queryString}`,
      cache: "no-store",
      next: { revalidate: 60, tags: ["get-users"] },
    }),
  );

  if (err) {
    return null;
  }

  const response = (await res.json()) as IGetUsersResponse;
  
  return response;
}
