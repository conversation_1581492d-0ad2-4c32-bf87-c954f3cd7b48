"use server";

import { nanoid } from "nanoid";
import { format } from "date-fns";
import { revalidateTag } from "next/cache";

import { client } from "@/utils/supabase/client";

import {
  updateUserProfileSchema,
  UpdateUserSchemaType,
} from "../schemas/update-user-profile.schema";

import { ActionState } from "@/types/action.type";
import { IGetUsersResult } from "@/types/response.type";
import { handleActionError } from "@/lib/handler-action-error";
import { flattenError } from "@/lib/zod";
import { tryCatch } from "@/utils/try-catch";
import { apiServer } from "@/lib/api-server";

type UpdateUserActionType = ActionState<UpdateUserSchemaType, IGetUsersResult>;

export async function updateAdminUserProfile(
  state: UpdateUserActionType,
  formData: FormData,
): Promise<UpdateUserActionType> {
  try {
    const payload: UpdateUserSchemaType = {
      id: formData.get("id") as string,
      is_staff: (formData.get("is_staff") as string) === "on",
      full_name: formData.get("full_name") as string,
      username: formData.get("username") as string,
      email: formData.get("email") as string,
      phone_number: formData.get("phone_number") as string,
      date_of_birth: formData.get("date_of_birth") as string,
      gender: formData.get("gender") as UpdateUserSchemaType["gender"],
      bio: formData.get("bio") as string,
      profile_picture: formData.get("profile_picture") as File,
    };

    const validateData = await updateUserProfileSchema.safeParseAsync(payload);

    if (!validateData.success) {
      return {
        success: false,
        message: "Periksa kembali data yang Anda masukkan",
        errors: flattenError(validateData.error).fieldErrors,
        inputs: payload,
      };
    }

    const { id, profile_picture, date_of_birth, ...rest } = validateData.data;

    const bucket = "user-profiles";
    const placeholder = nanoid(10);
    const path = `public/${placeholder}${profile_picture?.name ? `-${profile_picture.name}` : ""}`;

    if (
      profile_picture?.size &&
      profile_picture.size > 0 &&
      profile_picture.type?.startsWith("image/")
    ) {
      const supa = await client.storage.from(bucket).upload(path, profile_picture as File);

      if (supa.error) {
        console.log("supabase error", supa.error);

        return {
          success: false,
          message: "Error dengan gambar yang Anda upload.",
          errors: supa.error.message,
          inputs: payload,
        };
      }
    }

    const body = {
      ...rest,
      ...((profile_picture?.size || 0) > 0 && { profile_picture: path }),
      id,
      date_of_birth: format(new Date(date_of_birth), "yyyy-MM-dd"),
    };

    const [res, err] = await tryCatch(
      apiServer({
        method: "PATCH",
        url: `/api/admin/user-profiles/${id}/`,
        body: JSON.stringify(body),
      }),
    );

    if (err) {
      console.log("error update profile", err);
      return {
        success: false,
        message: "Gagal memperbarui profil",
        errors: err.message,
        inputs: payload,
      };
    }

    const response = await res.json();

    revalidateTag("get-users");

    console.log(body);
    console.log(`id: ${id}`, response);

    const isExistPhoneNumber = response.phone_number;
    const isExistEmail = response.email;
    const isExistUsername = response.username;

    if (
      typeof isExistPhoneNumber !== "string" ||
      typeof isExistEmail !== "string" ||
      typeof isExistUsername !== "string"
    ) {
      return {
        success: false,
        message: `Gagal memperbarui profile.`,
        errors: response as {
          phone_number: string[];
          email: string[];
          username: string[];
        },
        inputs: payload,
      };
    }

    if (!res.ok && profile_picture) {
      await client.storage.from(bucket).remove([path]);
    }

    return {
      success: true,
      message: "Berhasil memperbarui profil",
      inputs: {
        ...validateData.data,
        profile_picture: validateData.data.profile_picture as File,
      },
    };
  } catch (error) {
    const errors = handleActionError(error);
    return {
      success: false,
      message: "Unexpected Error",
      errors,
      inputs: state.inputs,
    };
  }
}
