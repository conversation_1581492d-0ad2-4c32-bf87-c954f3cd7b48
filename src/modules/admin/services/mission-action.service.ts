"use server";

import { handleActionError } from "@/lib/handler-action-error";
import { ActionState } from "@/types/action.type";
import { MissionSchemaType } from "../schemas/mission.schema";

type MissionActionType = ActionState<MissionSchemaType, string>;

export async function missionAction(
  state: MissionActionType,
  formData: FormData,
): Promise<MissionActionType> {
  try {
    return {
      success: true,
      message: "Berhasil menambahkan misi",
      data: "data",
    };
  } catch (error) {
    const errors = handleActionError(error);
    return {
      success: false,
      message: "Unexpected Error",
      errors,
      inputs: state.inputs,
    };
  }
}
