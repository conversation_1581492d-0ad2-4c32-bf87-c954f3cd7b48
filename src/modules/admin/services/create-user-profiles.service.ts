"use server";

import { revalidateTag } from "next/cache";
import { format } from "date-fns";
import { nanoid } from "nanoid";

import {
  createUserProfileSchema,
  CreateUserSchemaType,
} from "../schemas/create-user-profile.schema";

import { apiServer } from "@/lib/api-server";
import { flattenError } from "@/lib/zod";
import { handleActionError } from "@/lib/handler-action-error";

import { ActionState } from "@/types/action.type";
import { IGetUsersResult } from "@/types/response.type";

import { client } from "@/utils/supabase/client";
import { tryCatch } from "@/utils/try-catch";

type CreateUserActionType = ActionState<CreateUserSchemaType, IGetUsersResult>;

export async function createAdminUserProfile(
  state: CreateUserActionType,
  formData: FormData,
): Promise<CreateUserActionType> {
  try {
    const payload: CreateUserSchemaType = {
      full_name: formData.get("full_name") as string,
      username: formData.get("username") as string,
      email: formData.get("email") as string,
      phone_number: formData.get("phone_number") as string,
      date_of_birth: formData.get("date_of_birth") as string,
      gender: formData.get("gender") as CreateUserSchemaType["gender"],
      bio: formData.get("bio") as string,
      is_staff: (formData.get("is_staff") as string) === "on",
      profile_picture: formData.get("profile_picture") as File,
    };

    const validateData = await createUserProfileSchema.safeParseAsync(payload);

    if (!validateData.success) {
      return {
        success: false,
        message: "Periksa kembali data yang Anda masukkan",
        errors: flattenError(validateData.error).fieldErrors,
        inputs: payload,
      };
    }

    const { profile_picture, date_of_birth, ...rest } = validateData.data;

    const bucket = "user-profiles";
    const placeholder = nanoid(10);
    const path = `public/${placeholder}${profile_picture?.name ? `-${profile_picture.name}` : ""}`;

    if (
      profile_picture?.size &&
      profile_picture.size > 0 &&
      profile_picture.type?.startsWith("image/")
    ) {
      const supa = await client.storage.from(bucket).upload(path, profile_picture as File);

      if (supa.error) {
        console.log("supabase error", supa.error);

        return {
          success: false,
          message: "Error dengan gambar yang Anda upload.",
          errors: supa.error.message,
          inputs: payload,
        };
      }
    }

    const body = {
      ...rest,
      ...((profile_picture?.size || 0) > 0 && { profile_picture: path }),
      date_of_birth: format(new Date(date_of_birth), "yyyy-MM-dd"),
      is_active: true,
    };

    const [res, err] = await tryCatch(
      apiServer({
        method: "POST",
        url: "/api/admin/user-profiles/",
        body: JSON.stringify(body),
      }),
    );

    if (err) {
      console.log("error update profile", err);
      return {
        success: false,
        message: "Gagal memperbarui profil",
        errors: err.message,
        inputs: payload,
      };
    }

    const response = await res.json();

    revalidateTag("get-users");

    console.log(body);
    console.log(`response`, response);

    const isExistPhoneNumber = response.phone_number;
    const isExistEmail = response.email;
    const isExistUsername = response.username;

    if (
      typeof isExistPhoneNumber !== "string" ||
      typeof isExistEmail !== "string" ||
      typeof isExistUsername !== "string"
    ) {
      return {
        success: false,
        message: `Gagal memperbarui profile.`,
        errors: response as {
          phone_number: string[];
          email: string[];
          username: string[];
        },
        inputs: payload,
      };
    }

    if (response.detail) {
      return {
        success: false,
        message: response.detail,
        errors: response.detail,
        inputs: payload,
      };
    }

    if (!res.ok && profile_picture) {
      await client.storage.from(bucket).remove([path]);
    }

    return {
      success: true,
      message: "Berhasil memperbarui profil",
      inputs: {
        ...validateData.data,
        profile_picture: validateData.data.profile_picture as File,
      },
    };
  } catch (error) {
    const errors = handleActionError(error);
    return {
      success: false,
      message: "Unexpected Error",
      errors,
      inputs: state.inputs,
    };
  }
}
