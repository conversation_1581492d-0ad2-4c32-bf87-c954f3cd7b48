import { tryCatch } from "@/utils/try-catch";
import { apiServer } from "@/lib/api-server";
import { GamificationMission } from "@/types/response.type";

export async function getGamificationMission() {
  const [res, err] = await tryCatch(
    apiServer({
      method: "GET",
      url: "/api/gamification/missions",
      cache: "no-store",
      next: { revalidate: 60, tags: ["get-users"] },
    }),
  );

  if (err) {
    return null;
  }

  const response = (await res.json()) as Array<GamificationMission>;

  return response;
}
