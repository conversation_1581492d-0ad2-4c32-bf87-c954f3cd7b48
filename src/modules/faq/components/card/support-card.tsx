import React, { FC, ReactNode } from "react";
import { But<PERSON> } from "@/components/ui/button";
import clsx from "clsx";

interface SupportCardProps {
  icon: ReactNode;
  title: string;
  description: string;
  buttonText: string;
  href?: string;
  onClick?: () => void;
  className?: string;
}

const SupportCard: FC<SupportCardProps> = ({
  icon,
  title,
  description,
  buttonText,
  href,
  onClick,
  className,
}) => {
  return (
    <div className={clsx("rounded-lg bg-white/10 py-4 text-center", className)}>
      <div className="mx-auto flex h-16 w-16 items-center justify-center">{icon}</div>

      <h3 className="mb-2 font-semibold text-white">{title}</h3>
      <p className="mb-4 text-sm text-[#EDE9FE]">{description}</p>

      {href ? (
        <Button asChild variant="secondary" className="bg-white text-primary hover:bg-[#f3f4f6]">
          <a href={href} target="_blank" rel="noopener noreferrer">
            {buttonText}
          </a>
        </Button>
      ) : (
        <Button
          variant="secondary"
          className="bg-white text-primary hover:bg-[#f3f4f6]"
          onClick={onClick}
        >
          {buttonText}
        </Button>
      )}
    </div>
  );
};

export default SupportCard;
