"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { RiQ<PERSON>ionFill, RiRobot2Fill, RiSearchLine, RiSettings4Fill } from "@remixicon/react";
import { useState } from "react";
import { StrapiFaqSection } from "@/types/strapi.type";

const icons = {
  info: <RiQuestionFill className="h-6 w-6 text-[#7c3aed]" />,
  robot: <RiRobot2Fill className="h-6 w-6 text-[#7c3aed]" />,
  search: <RiSearchLine className="h-6 w-6 text-[#7c3aed]" />,
  setting: <RiSettings4Fill className="h-6 w-6 text-[#7c3aed]" />,
};

const FaqSectionView = ({ data }: { data: StrapiFaqSection }) => {
  const categories = ["Semua", ...data.categories.map((cat) => cat.title)];
  const [activeCategory, setActiveCategory] = useState("Semua");

  const filteredCategories =
    activeCategory === "Semua"
      ? data.categories
      : data.categories.filter((cat) => cat.title === activeCategory);

  return (
    <section className="py-6">
      <div className="mb-6 flex flex-wrap justify-center gap-3">
        {categories.map((category) => (
          <Button
            key={category}
            variant={activeCategory === category ? "default" : "outline"}
            className={
              activeCategory === category
                ? "rounded-full border-none bg-[#7c3aed] text-white hover:bg-[#6d28d9]"
                : "rounded-full border-none bg-[#f3f4f6] text-[#374151] hover:border-[#7c3aed] hover:text-primary"
            }
            onClick={() => setActiveCategory(category)}
          >
            {category}
          </Button>
        ))}
      </div>

      <div className="bg-[#F9FAFB] p-6">
        <div className="mx-auto max-w-3/5">
          {filteredCategories.map((category) => (
            <Card key={category.id} className="border-none bg-[#f9fafb] shadow-none">
              <CardContent className="px-6">
                <div className="mb-4 flex items-center space-x-3">
                  <div className="flex h-8 w-8 items-center justify-center">
                    {icons[category.icon] ?? <RiQuestionFill className="h-6 w-6 text-[#7c3aed]" />}
                  </div>
                  <h3 className="text-xl font-bold text-black">{category.title}</h3>
                </div>

                <Accordion type="single" collapsible className="space-y-2">
                  {category.faq_items.map((faq) => (
                    <AccordionItem
                      key={faq.id}
                      value={`${category.id}-${faq.id}`}
                      className="rounded-lg bg-white px-4 shadow-sm"
                    >
                      <AccordionTrigger className="py-4 text-left text-black hover:text-[#7c3aed]">
                        {faq.question}
                      </AccordionTrigger>
                      <AccordionContent className="pb-4 text-black">{faq.answer}</AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FaqSectionView;
