import { Ri<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ri<PERSON>hatsappLine } from "@remixicon/react";
import SupportCard from "../components/card/support-card";
import { StrapiFaqContactSection } from "@/types/strapi.type";

const icons = {
  chat: <RiChat3Fill className="h-8 w-8 text-white" />,
  wa: <RiWhatsappLine className="h-8 w-8 text-white" />,
  mail: <RiMailFill className="h-8 w-8 text-white" />,
};

const ContactFaqView = ({ data: contactSection }: { data: StrapiFaqContactSection }) => {
  return (
    <section className="bg-[#7c3aed] px-6 py-16">
      <div className="mx-auto max-w-4xl text-center">
        <h2 className="mb-4 text-3xl font-bold text-white">{contactSection.title}</h2>
        <p className="mx-auto mb-12 max-w-2xl text-xl text-[#ede9fe]">
          {contactSection.description}
        </p>

        <div className="grid gap-8 md:grid-cols-3">
          {contactSection.cards.map((card) => (
            <SupportCard
              key={card.id}
              icon={icons[card.icon] || <RiChat3Fill className="h-8 w-8 text-white" />}
              title={card.title}
              description={card.description}
              buttonText={card.buttonText}
              href={card.href}
              onClick={card.icon === "chat" ? () => console.log("Mulai Chat diklik") : undefined}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ContactFaqView;
