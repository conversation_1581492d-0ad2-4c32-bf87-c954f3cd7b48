import { Input } from "@/components/ui/input";
import { StrapiFaqHeroSection } from "@/types/strapi.type";
import { RiQuestionFill } from "@remixicon/react";
import { Search } from "lucide-react";

const FaqHeroView = ({ data: heroSection }: { data: StrapiFaqHeroSection }) => {
  return (
    <section className="from-secondary w-full bg-gradient-to-r to-[#FDF2F8] px-6 py-14">
      <div className="text-center">
        <div className="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-[#EDE9FE]">
          <RiQuestionFill className="h-10 w-10 text-primary" />
        </div>
        <h1 className="mb-4 text-5xl font-bold text-[#111827]">{heroSection.title}</h1>
        <p className="mx-auto mb-8 max-w-2xl text-xl text-[#374151]">{heroSection.description}</p>

        <div className="relative mx-auto mb-8 max-w-md">
          <Search className="absolute top-1/2 left-3 h-5 w-5 -translate-y-1/2 transform text-[#9ca3af]" />

          <Input
            placeholder="Cari pertanyaan..."
            className="border-[#ced4da] py-3 pl-10 focus:border-[#7c3aed] focus:ring-[#7c3aed]"
          />
        </div>
      </div>
    </section>
  );
};

export default FaqHeroView;
