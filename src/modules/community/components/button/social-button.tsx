import { RiD<PERSON>rd<PERSON>ill, Ri<PERSON>hatsappLine } from "@remixicon/react";
import clsx from "clsx";
import React, { FC } from "react";

type Service = "whatsapp" | "discord";

const icons = {
  whatsapp: RiWhatsappLine,
  discord: RiDiscordFill,
};

const colors = {
  whatsapp: ["bg-[#16a34a]", "hover:bg-[#15803d]"],
  discord: ["bg-[#4f46e5]", "hover:bg-[#4338ca]"],
};

interface Props extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  service: Service;
}

const SocialButton: FC<Props> = ({ service, className, children, ...rest }) => {
  const Icon = icons[service];
  const [bg, hover] = colors[service];

  return (
    <a
      className={clsx(
        "inline-flex items-center gap-2 rounded-lg px-8 py-3 text-base font-medium text-white transition",
        bg,
        hover,
        className,
      )}
      target="_blank"
      rel="noopener noreferrer"
      {...rest}
    >
      <Icon className="h-6 w-6" />
      {children}
    </a>
  );
};

export default SocialButton;
