import React, { <PERSON> } from "react";

import { Card } from "@/components/ui/card";
import { cn } from "@/utils/cn";

interface GuidelinesCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  iconBackground?: string;
  cardBackground?: string;
  className?: string;
}

const GuidelinesCard: FC<GuidelinesCardProps> = ({
  icon,
  title,
  description,
  iconBackground = "bg-[#16a34a]",
  cardBackground = "bg-[#dcfce7]",
  className,
}) => {
  return (
    <Card className={cn("border-none px-8 py-6 text-center", cardBackground, className)}>
      <div
        className={cn("mx-auto flex h-14 w-14 items-center justify-center rounded-full")}
        style={{ backgroundColor: iconBackground }}
      >
        {icon}
      </div>

      <h3 className="text-xl font-bold text-[#111827]">{title}</h3>
      <p className="text-[#6b7280]">{description}</p>
    </Card>
  );
};

export default GuidelinesCard;
