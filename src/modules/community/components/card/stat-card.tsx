import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/utils/cn";

interface StatCardProps {
  value: string;
  label: string;
  className?: string;
  valueColor?: string;
  labelColor?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  value,
  label,
  className = "",
  valueColor,
  labelColor = "text-[#374151]",
}) => (
  <Card className={cn("rounded-xl border-none py-0", className)}>
    <CardContent className="p-6 text-center">
      <div className={cn("mb-2 text-3xl font-bold", valueColor)}>{value}</div>
      <div className={cn("font-medium", labelColor)}>{label}</div>
    </CardContent>
  </Card>
);

export default StatCard;
