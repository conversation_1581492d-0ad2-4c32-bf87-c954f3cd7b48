import React from "react";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/utils/cn";

export interface TestimonyItemProps extends React.HTMLAttributes<HTMLDivElement> {
  name: string;
  message: string;
  imageUrl?: string;
  avatarColor?: string;
  textColor?: string;
  messageColor?: string;
  className?: string;
}

const TestimonyItem = ({
  name,
  message,
  imageUrl,
  avatarColor = "bg-[#f3f4f6] text-[#6b7280]",
  textColor = "text-[#000]",
  messageColor = "text-[#000]",
  className,
  ...rest
}: TestimonyItemProps) => {
  const initials = name
    .split(" ")
    .filter(Boolean)
    .slice(0, 2)
    .map((n) => n[0].toUpperCase())
    .join("");

  return (
    <div className={cn("flex flex-col gap-3 rounded-md bg-[#DCFCE7] p-2", className)} {...rest}>
      <div className="flex items-center gap-3">
        <Avatar className="h-8 w-8">
          {imageUrl ? (
            <AvatarImage src={imageUrl} alt={name} className="object-cover" />
          ) : (
            <AvatarFallback className={cn(avatarColor)}>{initials}</AvatarFallback>
          )}
        </Avatar>
        <div className={cn("text-sm font-semibold", textColor)}>{name}</div>
      </div>
      <div className={cn("text-sm", messageColor)}>{message}</div>
    </div>
  );
};

export default TestimonyItem;
