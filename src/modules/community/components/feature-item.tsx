import React, { FC, ReactNode } from "react";

import { cn } from "@/utils/cn";

interface FeatureItemProps {
  icon: ReactNode;
  title: string;
  subtitle?: string;
  iconColor?: string;
  className?: string;
}

const FeatureItem: FC<FeatureItemProps> = ({
  icon,
  title,
  subtitle,
  iconColor = "text-[#16a34a]",
  className,
}) => (
  <div className={cn("flex items-start gap-4", className)}>
    <div className={cn("flex h-10 w-10 flex-shrink-0 items-center justify-center", iconColor)}>
      {icon}
    </div>

    <div className="flex flex-col">
      <span className="font-semibold text-[#111827]">{title}</span>
      {subtitle && <span className="mt-1 text-sm text-[#6b7280]">{subtitle}</span>}
    </div>
  </div>
);

export default FeatureItem;
