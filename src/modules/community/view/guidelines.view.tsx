import { StrapiCommunityGuidelineSection } from "@/types/strapi.type";
import { <PERSON><PERSON><PERSON><PERSON>, HandHear<PERSON>, Shield } from "lucide-react";
import GuidelinesCard from "../components/card/guidelines-card";

const icons = {
  check: <CheckCircle className="h-8 w-8 text-white" />,
  heart: <HandHeart className="h-8 w-8 text-white" />,
  shield: <Shield className="h-8 w-8 text-white" />,
};

const GuidelinesCommunityView = ({
  data: guidelineSection,
}: {
  data: StrapiCommunityGuidelineSection;
}) => {
  return (
    <section className="px-6 py-16">
      <div className="mx-auto max-w-6xl">
        <div className="mb-12 text-center">
          <h2 className="mb-4 text-3xl font-bold text-[#111827]">{guidelineSection.title}</h2>
          <p className="mx-auto max-w-2xl text-[#6b7280]">{guidelineSection.description}</p>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {guidelineSection.cards.map((card) => {
            return (
              <GuidelinesCard
                key={card.id}
                icon={icons[card.icon]}
                title={card.title}
                description={card.description}
                iconBackground={card.iconBgColor}
                cardBackground={card.cardBgColor}
              />
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default GuidelinesCommunityView;
