import { StrapiCommunityHeroSection } from "@/types/strapi.type";
import SocialButton from "../components/button/social-button";
import StatCard from "../components/card/stat-card";

export function CommunityHeroView({ data: heroSection }: { data: StrapiCommunityHeroSection }) {
  return (
    <section className="w-full">
      <div className="from-secondary w-full bg-gradient-to-r to-[#FDF2F8] px-6 py-14 dark:bg-purple-800 dark:from-purple-600 dark:to-gray-900">
        <div className="mx-auto max-w-4xl text-center">
          <h1 className="text-foreground mb-6 text-5xl font-bold">{heroSection.title}</h1>
          <p className="text-secondary-foreground mx-auto mb-8 max-w-2xl text-xl font-normal">
            {heroSection.description}
          </p>

          <div className="flex flex-col justify-center gap-4 sm:flex-row">
            <SocialButton service="whatsapp" href={heroSection.buttonWaLink}>
              {heroSection.buttonWaText}
            </SocialButton>
            <SocialButton service="discord" href={heroSection.buttonDcLink}>
              {heroSection.buttonDcText}
            </SocialButton>
          </div>
        </div>
      </div>

      <div className="px-6 py-14">
        <div className="mx-auto grid max-w-6xl grid-cols-4 gap-6">
          {heroSection.cards.map((card) => (
            <StatCard
              key={card.id}
              className={card.bgColor}
              valueColor={card.valueColor}
              value={card.value}
              label={card.text}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
