import { RiCheckboxCircleFill, RiD<PERSON>rdFill } from "@remixicon/react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";

import SocialButton from "../components/button/social-button";
import FeatureItem from "../components/feature-item";

import { StrapiCommunityDiscordSection } from "@/types/strapi.type";
import { STRAPI_URL } from "@/lib/strapi";
import { cn } from "@/utils/cn";

const DiscordServerCommunityView = ({
  data: discordSection,
}: {
  data: StrapiCommunityDiscordSection;
}) => {
  const avatarUrl = discordSection.cards.avatar?.url;

  return (
    <section className="bg-[#EEF2FF] px-6 py-16">
      <div className="mx-auto grid max-w-7xl grid-cols-1 items-center gap-12 lg:grid-cols-2">
        <div className="mx-auto flex w-full max-w-sm flex-col rounded-2xl bg-[#111827] p-6 text-white">
          <div className="mb-6 flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-[#4f46e5]">
              <RiDiscordFill className="h-6 w-6 text-white" />
            </div>
            <div>
              <div className="font-bold">{discordSection.cards.title}</div>
              <div className="text-sm text-[#9ca3af]">● {discordSection.cards.members} online</div>
            </div>
          </div>

          <div className="mb-6 space-y-2 text-sm">
            {discordSection?.listRoomDc?.map((room) => (
              <div
                key={room.id}
                className={cn(
                  "flex items-center gap-2 rounded px-2 py-1",
                  room.isActive ? "bg-[#4f46e5] text-white" : "text-[#9ca3af]",
                )}
              >
                <span>{room.icon === "voice" ? "🔊" : "#"}</span>
                <span>{room.text}</span>
              </div>
            ))}
          </div>

          <div className="mt-auto flex items-center gap-2 border-t border-[#374151] pt-4">
            <Avatar className="h-8 w-8">
              <AvatarImage src={avatarUrl ? `${STRAPI_URL}${avatarUrl}` : undefined} />
              <AvatarFallback className="bg-[#4f46e5] text-white">
                {discordSection.cards.name?.[0]?.toUpperCase() || "U"}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm">{discordSection.cards.name}</span>
            <div className="ml-auto">
              <Button variant="ghost" size="icon" className="h-6 w-6 text-[#9ca3af]">
                ⚙️
              </Button>
            </div>
          </div>
        </div>

        <div>
          <div className="mb-6 flex items-center gap-3">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-[#4f46e5]">
              <RiDiscordFill className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-[#111827]">{discordSection.title}</h2>
              <p className="font-medium text-[#4f46e5]">{discordSection.subTitle}</p>
            </div>
          </div>

          <p className="mb-8 text-[#6b7280]">{discordSection.description}</p>

          <div className="mb-8 space-y-4">
            {discordSection.list.map((item) => (
              <FeatureItem
                key={item.id}
                icon={<RiCheckboxCircleFill />}
                title={item.text}
                subtitle={item.subText}
                iconColor="text-[#4f46e5]"
              />
            ))}
          </div>

          <SocialButton service="discord" href={discordSection.joinDcLink}>
            {discordSection.joinDcText}
          </SocialButton>
        </div>
      </div>
    </section>
  );
};

export default DiscordServerCommunityView;
