import { StrapiCommunityWhatsappSection } from "@/types/strapi.type";
import { RiCheckboxCircleFill, RiWhatsappLine } from "@remixicon/react";
import SocialButton from "../components/button/social-button";
import FeatureItem from "../components/feature-item";
import TestimonyItem from "../components/testimony-item";
import { STRAPI_URL } from "@/lib/strapi";

const WhatsAppCommunityView = ({
  data: whatsAppSection,
}: {
  data: StrapiCommunityWhatsappSection;
}) => {
  return (
    <section className="bg-[#F0FDF4] px-6 py-16">
      <div className="mx-auto grid max-w-7xl grid-cols-1 items-center gap-12 lg:grid-cols-2">
        <div>
          <div className="mb-6 flex items-center gap-3">
            <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-[#16a34a]">
              <RiWhatsappLine className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-3xl font-bold text-[#111827]">{whatsAppSection.title}</h2>
              <p className="text-base font-medium text-[#16a34a]">{whatsAppSection.subTitle}</p>
            </div>
          </div>

          <p className="mb-8 text-lg font-normal text-[#4B5563]">{whatsAppSection.description}</p>

          <div className="mb-8 space-y-4">
            {whatsAppSection.list.map((item) => (
              <FeatureItem
                key={item.id}
                icon={<RiCheckboxCircleFill />}
                title={item.text}
                subtitle={item.subText}
              />
            ))}
          </div>

          <SocialButton service="whatsapp" href={whatsAppSection.joinWaLink}>
            {whatsAppSection.joinWaText}
          </SocialButton>
        </div>

        <div className="mx-auto max-w-3/5 rounded-2xl bg-white p-6 shadow-lg">
          <div className="mb-4 flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-[#16a34a]">
              <RiWhatsappLine className="h-6 w-6 text-white" />
            </div>
            <div>
              <div className="font-medium text-[#111827]">{whatsAppSection.card.title}</div>
              <div className="text-sm text-[#6b7280]">{whatsAppSection.card.members} members</div>
            </div>
          </div>

          <div className="space-y-4">
            {whatsAppSection.messages.map((msg, index) => {
              const avatarUrl = msg.avatar?.url ? `${STRAPI_URL}${msg.avatar.url}` : undefined;

              return (
                <TestimonyItem
                  key={msg.id}
                  name={msg.name}
                  imageUrl={avatarUrl}
                  message={msg.message}
                  className={index % 2 === 1 ? "bg-[#E5E7EB]" : ""}
                />
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhatsAppCommunityView;
