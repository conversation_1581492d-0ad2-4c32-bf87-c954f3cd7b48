import { tryCatch } from "@/utils/try-catch";
import { ISchedule } from "@/types/response.type";
import { apiServer } from "@/lib/api-server";

export async function getScheduleDate(date: string) {
  const [resGetDate, errGetDate] = await tryCatch(
    apiServer({
      method: "GET",
      url: `/api/get-available-schedules/${date}`,
    }),
  );

  if (errGetDate) {
    console.error(`Error #%d get schedule date: ${errGetDate.message}`);
    return null;
  }

  const response = await resGetDate.json();

  if (!resGetDate.ok) {
    console.log("Error get schedule date", response);
    return null;
  }

  return response as ISchedule[];
}
