import { motion } from "motion/react";

import { LoadingBubble } from "@/components/shared/loading-bubble";
import { fadeIn, mountAnim } from "@/utils/animation";

export function ScheduleOverlay() {
  return (
    <motion.div {...mountAnim(fadeIn)} className="fixed top-0 left-0 z-50 size-full">
      <div className="absolute top-0 left-0 size-full rounded-xl bg-black/20 backdrop-blur-[2px]" />
      <div className="sticky top-1/2 flex -translate-y-1/2 flex-col items-center justify-center">
        <p className="text-muted-foreground text-2xl font-bold"><PERSON><PERSON>.</p>
        <p className="text-muted-foreground mb-2 text-sm">Kami sedang menyiapkan jadwal Anda.</p>

        <LoadingBubble
          amount={6}
          turnStatus="none"
          className={{ bubble: "bg-muted-foreground size-3", wrapper: "gap-2" }}
        />
      </div>
    </motion.div>
  );
}
