"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useCallback } from "react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { IInterview } from "@/types/response.type";

export function ButtonInterview({
  status,
  bookingCode,
  label,
}: {
  status: IInterview["status"];
  bookingCode: string;
  label?: string;
}) {
  const router = useRouter();
  const { data } = useSession();

  const handleClick = useCallback(() => {
    if (status === "Scheduled") {
      const { email_verified, phone_number_verified } = data?.user ?? {};

      if (!email_verified || !phone_number_verified) {
        toast.warning(
          !email_verified && !phone_number_verified
            ? "Belum melakukan verifikasi Email dan Nomor HP"
            : !email_verified
              ? "Belum melakukan verifikasi Email"
              : "Belum melakukan verifikasi Nomor HP",
          {
            description: `Silakan verifikasi ${
              !email_verified && !phone_number_verified
                ? "Email dan Nomor <PERSON>"
                : !email_verified
                  ? "Email"
                  : "Nomor HP"
            } terlebih dahulu.`,
          },
        );
        return;
      }

      router.push(`/dashboard/interview/${bookingCode}`);
    } else {
      router.push(`/dashboard/interview/${bookingCode}/detail`);
    }
  }, [status, bookingCode, data, router]);

  return (
    <Button size="sm" disabled={status === "Pending"} onClick={handleClick}>
      {label || "Lihat Hasil"}
    </Button>
  );
}
