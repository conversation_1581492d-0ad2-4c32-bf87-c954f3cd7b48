import Link from "next/link";
import { ArrowR<PERSON>, Briefcase, CalendarCheck, Cog } from "lucide-react";

import { <PERSON>, CardA<PERSON>, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/utils/cn";

export function CardExtras() {
  const extras = [
    {
      title: "Jadwal Interview",
      description: "Atur jadwal simulasi wawancara sesuai waktu yang Anda inginkan",
      icon: <CalendarCheck />,
      label: "Jadwal Interview",
      href: "/dashboard/interview/create",
    },
    {
      title: "<PERSON><PERSON>",
      description: "<PERSON><PERSON><PERSON><PERSON> peluang karir dan posisi yang sesuai dengan keahlian <PERSON>",
      icon: <Briefcase />,
      label: "<PERSON><PERSON>",
      href: "/dashboard/work",
    },
    {
      title: "Pengaturan",
      description: "Ke<PERSON><PERSON> profil, preferensi, dan pengaturan akun <PERSON>",
      icon: <Cog />,
      label: "Pengaturan",
      href: "/dashboard/setting",
    },
  ];

  return (
    <div className="grid grid-cols-1 gap-6 font-sans lg:grid-cols-3">
      {extras.map((item, index) => {
        const iconVariants = {
          "Jadwal Interview": "bg-blue-50 text-blue-500 ",
          "Cari Pekerjaan": "bg-green-50 text-green-500 ",
          Pengaturan: "bg-purple-50 text-purple-500 ",
        };

        const linkVariants = {
          "Jadwal Interview": "text-blue-600 hover:text-blue-500 ",
          "Cari Pekerjaan": "text-green-600 hover:text-green-500 ",
          Pengaturan: "text-purple-600 hover:text-purple-500 ",
        };

        return (
          <Card key={index} className="shadow-none">
            <CardHeader>
              <div className="space-y-2">
                <CardTitle className="text-lg">{item.title}</CardTitle>
                <p className="text-muted-foreground max-w-[30ch] text-sm">{item.description}</p>
              </div>

              <CardAction>
                <div
                  className={cn(
                    "grid size-12 place-content-center rounded-xl",
                    iconVariants[item.title as keyof typeof iconVariants],
                  )}
                >
                  {item.icon}
                </div>
              </CardAction>
            </CardHeader>

            <CardFooter>
              <CardAction>
                <Link
                  href={item.href}
                  className={cn(
                    "flex items-center gap-2 text-sm leading-none font-medium transition-colors hover:underline",
                    linkVariants[item.title as keyof typeof linkVariants],
                  )}
                >
                  {item.label}
                  <ArrowRight className="size-4" />
                </Link>
              </CardAction>
            </CardFooter>
          </Card>
        );
      })}
    </div>
  );
}
