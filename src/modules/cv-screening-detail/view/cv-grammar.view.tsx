import { <PERSON>, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader } from "@/components/ui/card";
import { RichText } from "@/components/ui/rich-text";
import { ICVScreeningReportResponse } from "@/types/response.type";

export function CVGrammar({ data }: { data: ICVScreeningReportResponse }) {
  return (
    <Card className="max-h-[400px] overflow-hidden shadow-none gap-2">
      <CardHeader className="border-b">
        <h3 className="text-lg font-bold">Analisis Grammar CV</h3>
        <CardDescription>
          Berikut adalah daftar kesalahan grammar yang ditemukan dalam CV Anda
        </CardDescription>
      </CardHeader>

      <CardContent className="no-scrollbar overflow-auto">
        <div className="sticky top-0 h-4 bg-gradient-to-b from-white to-transparent" />
        <ul className="space-y-4">
          {data.grammar.map((item, index) => (
            <li
              key={index}
              className="flex flex-col gap-2.5 rounded-xl border p-4 text-sm md:text-base"
            >
              <h4 className="block leading-none">{item.label}</h4>
              {/* <p className="text-muted-foreground">{item.detail}</p> */}
              <div>
                <RichText content={item.detail} />
              </div>
            </li>
          ))}
        </ul>
        <div className="sticky bottom-0 h-4 bg-gradient-to-t from-white to-transparent" />
      </CardContent>
      <CardFooter>
        <p className="ml-auto text-xs">Total {data.grammar.length} kesalahan grammar</p>
      </CardFooter>
    </Card>
  );
}
