import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { ICVScreeningReportResponse } from "@/types/response.type";
import Image from "next/image";

export function CVSummaryView({ data }: { data: ICVScreeningReportResponse }) {
  return (
    <Card className="relative overflow-hidden shadow-none">
      <CardHeader className="border-b">
        <h1 className="text-lg font-bold">Ringkasan Analisis CV</h1>
      </CardHeader>
      <CardContent>
        <div className="relative max-w-[60ch] md:text-lg">{data.summary}</div>
        <Image
          src="/images/LOGO 2.png"
          alt="Logo"
          width={600}
          height={400}
          className="absolute right-4 bottom-0 translate-y-[35%] opacity-10"
        />
      </CardContent>
    </Card>
  );
}
