"use client";

import Image from "next/image";
import { toast } from "sonner";
import { useActionState, useEffect } from "react";
import { CheckCircle, Clock, Download, Zap } from "lucide-react";

import { generateCV } from "../../services/cv-generate.service";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

import { initialActionState } from "@/types/action.type";
import { formatBytes } from "@/hooks/use-file-upload";
import { cn } from "@/utils/cn";

const LIST = [
  "Template profesional yang disesuaikan",
  "Optimasi kata kunci untuk ATS",
  "Penyesuaian konten berdasarkan laporan analisis",
  "Saran konten berdasarkan industri",
  "Format yang mudah dibaca HR",
];

export function CVGenerateCard({
  id,
  data,
  isGenerated,
}: {
  id: string;
  data: Blob | { error: string };
  isGenerated: boolean;
}) {
  const [state, action, pending] = useActionState(generateCV, initialActionState);

  useEffect(() => {
    if (state.success && state.message) {
      toast.success(state.message, {
        description: state.data,
      });
    }

    if (!state.success && state.message) {
      toast.error(state.message);
    }
  }, [state.success, state.message]);

  return (
    <Card className="h-fit w-full shadow-none">
      <CardHeader
        className={cn(
          "flex flex-col items-center justify-center gap-2",
          data instanceof Blob ? "border-b" : "",
        )}
      >
        <div className="bg-muted mb-4 rounded-full border p-4">
          <Image src="/web-app-manifest-512x512.png" alt="Logo" width={48} height={48} />
        </div>

        <CardTitle className="text-center">AI-Generated CV</CardTitle>
        <CardDescription className="mb-2 text-center">
          Buat CV profesional dengan bantuan AI dalam hitungan menit
        </CardDescription>
        <Badge
          variant="secondary"
          className="bg-gradient-purple text-primary-foreground border-none"
        >
          <Zap className="mr-1 size-3" /> Powered by AI
        </Badge>
      </CardHeader>

      {isGenerated && data instanceof Blob ? (
        <CardContent>
          <div className="space-y-6">
            <div className="flex items-end justify-between">
              <div className="max-w-[20ch]">
                <p className="text-sm font-bold">Generated CV</p>
                <p className="text-muted-foreground text-xs">
                  Siap untuk diunggah ke platform lowongan kerja.
                </p>
              </div>

              <Badge variant="secondary" className="text-xs">
                {formatBytes(data.size, 1)} {data.type.split("/")[1].toUpperCase()}
              </Badge>
            </div>

            <Button
              className="w-full"
              onClick={() => {
                window.open("/api/cv-builder/" + id, "_blank");
              }}
            >
              <Download /> Download
            </Button>
          </div>
        </CardContent>
      ) : (
        <CardContent>
          <ul className="mb-6 space-y-3">
            {LIST.map((item, idx) => {
              return (
                <li key={idx} className="flex items-center gap-3 text-sm">
                  <CheckCircle className="text-primary h-4 w-4 shrink-0" />
                  <span className="text-primary">{item}</span>
                </li>
              );
            })}
          </ul>

          <form action={action}>
            <input type="text" name="id" defaultValue={id} hidden />
            <Button
              type="submit"
              disabled={pending || state.data === "CV Builder started."}
              className="bg-gradient-purple mb-2 w-full hover:opacity-80"
            >
              {state.data === "CV Builder started." ? (
                <>
                  <Clock className="size-3" /> Generating CV...
                </>
              ) : (
                "Generate CV Sekarang"
              )}
            </Button>
          </form>

          <p className="text-muted-foreground text-center text-xs">
            Fitur ini akan memakan waktu sekitar 2 menit.
          </p>
        </CardContent>
      )}
    </Card>
  );
}
