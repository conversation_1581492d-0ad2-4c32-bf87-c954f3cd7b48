"use server";

import { apiServer } from "@/lib/api-server";
import { tryCatch } from "@/utils/try-catch";
import { ActionState } from "@/types/action.type";
import { rewards } from "@/modules/gamification/services/rewards.service";

type GenerateCVActionType = ActionState<string, string>;

export async function generateCV(
  state: GenerateCVActionType,
  formData: FormData,
): Promise<GenerateCVActionType> {
  try {
    const id = formData.get("id") as string;

    const [res, err] = await tryCatch(
      apiServer({
        method: "POST",
        url: `/api/cv-screening/report/${id}/build/`,
        body: JSON.stringify({
          webhook_url: process.env.NEXT_PUBLIC_SITE_URL + "/api/cv-builder/" + id,
        }),
      }),
    );

    if (err) {
      return {
        success: false,
        message: "Gagal mengenerate CV",
      };
    }

    const data = await res.json();

    if (!res.ok) {
      console.log(data);

      return {
        success: false,
        message: "Gagal mengenerate CV",
      };
    }

    await rewards("finished_cv_builder");

    return {
      success: true,
      message: "Berhasil mengenerate CV",
      data: data.message,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      message: "Unexpected Error",
    };
  }
}
