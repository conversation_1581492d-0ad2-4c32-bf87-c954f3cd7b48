import { Button } from "@/components/ui/button";
import { StrapiPricingHeroSection } from "@/types/strapi.type";

const HeroPricingView = ({ data: heroSection }: { data: StrapiPricingHeroSection }) => {
  return (
    <section className="from-secondary w-full bg-gradient-to-r to-[#FDF2F8] px-6 py-14">
      <div className="mx-auto max-w-4xl text-center">
        <h1 className="mb-4 text-5xl font-bold text-[#111827]">{heroSection.title}</h1>
        <p className="mx-auto mb-8 max-w-2xl text-xl text-[#374151]">{heroSection.description}</p>
        <div className="mx-auto mb-12 flex w-fit items-center justify-center rounded-full bg-white p-1 font-medium">
          <Button className="rounded-full bg-[#7c3aed] text-white hover:bg-[#6d28d9]">
            {heroSection.buttonTextMonthly}
          </Button>
          <Button className="rounded-full bg-white text-[#4B5563]" variant="ghost">
            {heroSection.buttonTextYearly}
            <span className="ml-1 text-[#16A34A]">(Hemat 30%)</span>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default HeroPricingView;
