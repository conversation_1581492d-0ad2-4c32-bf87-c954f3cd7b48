import React from "react";
import FaqItem from "../components/faq-item";
import { StrapiPricingFaqSection } from "@/types/strapi.type";

const FaqPricingView = ({ data: faqSection }: { data: StrapiPricingFaqSection }) => {
  return (
    <section className="px-6 py-16">
      <div className="mx-auto max-w-3/5">
        <h2 className="mb-4 text-center text-3xl font-bold text-[#111827]">{faqSection.title}</h2>
        <p className="mb-12 text-center text-[#4b5563]">{faqSection.description}</p>

        <div className="space-y-8">
          {faqSection.faqs.map((item) => (
            <FaqItem key={item.id} question={item.question} answer={item.answer} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default FaqPricingView;
