import { Button } from "@/components/ui/button";
import { StrapiPricingGuaranteeSection } from "@/types/strapi.type";
import { Shield } from "lucide-react";
import Link from "next/link";
import React from "react";

const GuaranteePricingView = ({
  data: guaranteeSection,
}: {
  data: StrapiPricingGuaranteeSection;
}) => {
  return (
    <section className="bg-[#7C3AED] py-16">
      <div className="mx-auto max-w-4xl px-4 text-center sm:px-6 lg:px-8">
        <div className="mx-auto mb-6 w-fit rounded-full bg-white/20 p-4">
          <Shield className="mx-auto h-8 w-8 text-white" />
        </div>
        <h2 className="mb-4 text-3xl font-bold text-white md:text-4xl">{guaranteeSection.title}</h2>
        <p className="mx-auto mb-8 max-w-2xl text-xl text-white/90">
          {guaranteeSection.description}
        </p>
        <Button
          className="bg-white px-8 py-3 font-medium text-primary hover:bg-[#f3f4f6]"
          asChild
        >
          <Link href="#">{guaranteeSection.buttonText}</Link>
        </Button>
      </div>
    </section>
  );
};

export default GuaranteePricingView;
