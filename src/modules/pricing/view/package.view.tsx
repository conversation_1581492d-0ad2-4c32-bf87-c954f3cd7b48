import { StrapiPricingSection } from "@/types/strapi.type";
import PricingCard from "../components/pricing-card";

const PackagePricingView = ({ data }: { data: StrapiPricingSection }) => {
  return (
    <section className="px-6 py-16">
      <div className="mx-auto max-w-6xl">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
          {data.plans.map((plan) => (
            <PricingCard
              key={plan.id}
              title={plan.title}
              price={
                plan.price === "Custom"
                  ? "Custom"
                  : `Rp ${Number(plan.price).toLocaleString("id-ID")}`
              }
              description={plan.description}
              features={plan.lists.map((list) => ({
                label: list.label,
                included: list.isIncluded,
              }))}
              buttonText={plan.buttonText}
              buttonVariant={plan.buttonVariant as "default" | "outline"}
              buttonStyle={plan.buttonStyle}
              cardStyle={plan.cardStyle}
              highlight={plan.isPopuler}
              textColor={plan.textColor ?? "text-black"}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default PackagePricingView;
