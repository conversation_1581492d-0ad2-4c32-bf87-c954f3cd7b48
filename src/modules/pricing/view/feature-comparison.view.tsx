import { StrapiPricingFeatureSection } from "@/types/strapi.type";
import { Check, X } from "lucide-react";
import React from "react";

const FeatureComparisonPricingView = ({ data }: { data: StrapiPricingFeatureSection }) => {
  const renderFeatureValue = (value: string) => {
    const trimmed = value.trim();
    if (trimmed === "✔️") {
      return <Check className="mx-auto h-5 w-5 text-[#16a34a]" />;
    } else if (trimmed === "❌") {
      return <X className="mx-auto h-5 w-5 text-[#ef4444]" />;
    } else {
      return <span>{value}</span>;
    }
  };

  return (
    <section className="bg-[#F9FAFB] px-6 py-16">
      <div className="mx-auto max-w-6xl">
        <h2 className="mb-4 text-center text-3xl font-bold text-[#111827]">{data.title}</h2>
        <p className="mb-12 text-center text-lg text-[#4b5563]">{data.description}</p>

        <div className="overflow-hidden rounded-lg border border-[#e5e7eb] bg-white">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-[#f9fafb]">
                <tr>
                  <th className="px-6 py-4 text-left font-medium text-[#111827]">Fitur</th>
                  <th className="px-6 py-4 text-center font-medium text-[#111827]">Free</th>
                  <th className="bg-[#F5F3FF] px-6 py-4 text-center font-medium text-[#111827]">
                    Pro
                  </th>
                  <th className="px-6 py-4 text-center font-medium text-[#111827]">Enterprise</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-[#e5e7eb]">
                {data.features.map((feature, index) => (
                  <tr key={feature.id} className={index % 2 === 1 ? "bg-[#f9fafb]" : ""}>
                    <td className="px-6 py-4 text-[#4b5563]">{feature.featureName}</td>
                    <td className="px-6 py-4 text-center text-[#4b5563]">
                      {renderFeatureValue(feature.freeValue)}
                    </td>
                    <td className="bg-[#F5F3FF] px-6 py-4 text-center text-[#4b5563]">
                      {renderFeatureValue(feature.proValue)}
                    </td>
                    <td className="px-6 py-4 text-center text-[#4b5563]">
                      {renderFeatureValue(feature.enterpriseValue)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeatureComparisonPricingView;
