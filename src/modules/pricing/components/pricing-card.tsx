import { Check, X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import clsx from "clsx";
import React from "react";

interface Feature {
  label: string;
  included: boolean;
}

interface PricingCardProps {
  title: string;
  price: React.ReactNode;
  description: string;
  features: Feature[];
  buttonText: string;
  buttonVariant?: "default" | "outline";
  onClick?: () => void;
  highlight?: boolean;
  highlightText?: string;
  cardStyle?: string;
  buttonStyle?: string;
  textColor?: string;
  badgeColor?: string;
}

const PricingCard = ({
  title,
  price,
  description,
  features,
  buttonText,
  buttonVariant = "default",
  onClick,
  highlight = false,
  highlightText = "Most Popular",
  cardStyle = "",
  buttonStyle = "",
  textColor = "text-black",
  badgeColor = "bg-[#facc15]",
}: PricingCardProps) => {
  return (
    <Card className={clsx("relative border border-[#E5E7EB] bg-white", cardStyle)}>
      {highlight && (
        <div className="absolute -top-3 left-1/2 -translate-x-1/2 transform">
          <Badge className={clsx("font-medium text-[#111827]", badgeColor)}>{highlightText}</Badge>
        </div>
      )}
      <CardHeader className={clsx("pb-2 text-center", textColor)}>
        <h3 className="text-2xl font-bold">{title}</h3>
        <div className="">
          <span className="text-4xl font-bold">{price}</span>
        </div>
        <p className="text-base">{description}</p>
      </CardHeader>
      <CardContent className={clsx("space-y-4", textColor)}>
        {features.map((feature, idx) => (
          <div key={idx} className="flex items-center space-x-3">
            {feature.included ? (
              <Check className="h-5 w-5 text-[#22C55E]" />
            ) : (
              <X className="h-5 w-5 text-[#9ca3af]" />
            )}
            <span className={feature.included ? "" : "text-[#9ca3af]"}>{feature.label}</span>
          </div>
        ))}
        <div className="pt-6">
          <Button variant={buttonVariant} className={clsx("w-full", buttonStyle)} onClick={onClick}>
            {buttonText}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default PricingCard;
