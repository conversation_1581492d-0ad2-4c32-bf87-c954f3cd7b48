import { FC } from "react";

interface FaqItemProps {
  question: string;
  answer: string;
}

const FaqItem: FC<FaqItemProps> = ({ question, answer }) => {
  return (
    <div className="rounded-md bg-[#F9FAFB] p-6">
      <h3 className="mb-2 text-base font-semibold text-[#111827]">{question}</h3>
      <p className="text-base font-normal text-[#4b5563]">{answer}</p>
    </div>
  );
};

export default FaqItem;
