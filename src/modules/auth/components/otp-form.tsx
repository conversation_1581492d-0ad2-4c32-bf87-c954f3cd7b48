"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useSession } from "next-auth/react";
import { startTransition, useActionState, useCallback, useEffect, useState } from "react";
import { OTPInput, SlotProps } from "input-otp";
import { RotateCw } from "lucide-react";
import { RiCheckboxCircleFill, RiErrorWarningFill } from "@remixicon/react";
import { toast } from "sonner";

import { verifyOtp } from "../services/verify.service";

import { Timer } from "@/components/ui/timer";
import { Button } from "@/components/ui/button";

import { cn } from "@/utils/cn";
import { initialActionState } from "@/types/action.type";

const TIME_LIMIT = 60 * 5; // 5 minutes

export function OtpForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session, update } = useSession();

  const type = searchParams.get("type") as "email" | "phone";

  const [time, setTime] = useState(TIME_LIMIT);
  const [attempt, setAttempt] = useState(1);
  const [otpValue, setOtpValue] = useState("".trim());
  const [state, action, isPending] = useActionState(verifyOtp, initialActionState);

  useEffect(() => {
    if (time === 0) {
      toast.warning("Kode OTP telah kadaluwarsa", {
        description: "Silakan minta kode baru.",
      });
    }
  }, [time]);

  useEffect(() => {
    if (state.success && state.data) {
      toast.success(state.data, {
        description: "Sukses verifikasi! anda dapat melanjutkan.",
      });

      update({
        name: session?.user.name,
        phone_number: session?.user.phone_number,
        date_of_birth: session?.user.date_of_birth,
        gender: session?.user.gender,
        bio: session?.user.bio,
        image: session?.user.image,
        is_onboarded: session?.user.is_onboarded,
        email_verified: type === "email" ? true : session?.user.email_verified,
        phone_number_verified: type === "phone" ? true : session?.user.phone_number_verified,
      }).then(() => {
        router.push("/dashboard");
      });
    }

    if (!state.success && state.message) {
      setOtpValue("");
      toast.error(state.message, {
        description: state.errors,
      });
    }
  }, [state.success, state.data, session?.user, state.message, state.errors, state.inputs]);

  useEffect(() => {
    console.log(attempt);
  }, [attempt]);

  const handleResend = async () => {
    setTime(TIME_LIMIT);

    switch (type) {
      case "email":
        await fetch("/api/verification/email", {
          method: "POST",
        });
        toast.success("Kode OTP telah dikirim", {
          description: "Kode telah dikirim ke email Anda.",
        });
        break;
      case "phone":
        await fetch("/api/verification/phone", {
          method: "POST",
        });
        toast.success("Kode OTP telah dikirim", {
          description: "Kode telah dikirim ke Whatsapp Anda.",
        });
        break;
      default:
        toast.error("Tidak dapat mengirim kode", {
          description: "Silakan coba lagi nanti.",
        });
        break;
    }
  };

  const handleSubmit = useCallback(
    (e: React.FormEvent<HTMLFormElement>) => {
      setAttempt((prev) => prev + 1);
      e.preventDefault();

      const formData = new FormData(e.currentTarget);

      formData.append("type", type);
      formData.append("otp", otpValue);
      formData.append("attempt", attempt.toString());

      startTransition(() => {
        action(formData);
      });
    },
    [otpValue, attempt, type, action],
  );

  const handleOtpChange = useCallback(
    (value: string) => {
      if (!state.success && !isPending) {
        setOtpValue(value.trim());
      }
    },
    [state.success, isPending],
  );

  return (
    <form onSubmit={handleSubmit} className="w-full space-y-6">
      <div className="flex flex-col">
        <div className="mb-6 flex flex-col gap-2">
          {!state.success && typeof state.errors === "string" && (
            <p className="text-destructive text-center text-xs">{state.errors}</p>
          )}

          <OTPInput
            name="otp"
            value={otpValue}
            onChange={handleOtpChange}
            required
            maxLength={6}
            render={({ slots }) => (
              <div className="flex gap-2">
                {slots.map((slot, idx) => (
                  <Slot key={idx} {...slot} error={Boolean(!state.success && state.errors)} />
                ))}
              </div>
            )}
          />
        </div>

        <div className="mb-4 flex w-full flex-col items-center justify-center gap-4">
          <div className="bg-muted flex h-25 w-full flex-col items-center justify-center gap-2 rounded-md">
            <p className="text-muted-foreground">Kode akan kadaluwarsa dalam:</p>
            <Timer
              time={time}
              setTime={setTime}
              className="text-2xl font-bold text-purple-700 md:text-3xl"
            />
          </div>

          <div className="flex w-full flex-col items-center justify-center gap-2">
            <p className="text-muted-foreground text-sm">Tidak menerima Kode?</p>
            <button
              type="reset"
              onClick={handleResend}
              disabled={time > 0 || state.success}
              className="group flex items-center gap-2 text-purple-700 hover:underline disabled:pointer-events-none disabled:opacity-50"
            >
              <RotateCw className="size-4 transition-transform duration-500 group-hover:rotate-360" />
              <p className="text-sm font-medium">Kirim ulang kode</p>
            </button>
          </div>
        </div>

        <Button
          type="submit"
          disabled={otpValue.length !== 6 || isPending}
          className="bg-gradient-purple disabled:bg-muted mb-2 h-12 w-full font-bold hover:opacity-80"
        >
          <RiCheckboxCircleFill className="size-4" /> Verifikasi & Lanjutkan
        </Button>
      </div>

      <div className="flex gap-2 rounded-md border border-amber-200 bg-amber-50 p-4 text-amber-700">
        <RiErrorWarningFill className="size-4" />

        <div className="space-y-2">
          <p className="leading-none font-medium">Tips keamanan:</p>
          <ul className="list-inside list-disc space-y-1 text-sm">
            <li>Jangan bagikan kode OTP kepada siapapun</li>
            <li>Kode hanya berlaku selama 5 menit</li>
            <li>Pastikan Anda berada di situs resmi</li>
          </ul>
        </div>
      </div>
    </form>
  );
}

function Slot(props: SlotProps & { error?: boolean }) {
  return (
    <div
      className={cn(
        "border-input bg-background text-foreground flex aspect-square flex-1 items-center justify-center rounded-md border font-medium shadow-xs transition-[color,box-shadow]",
        props.isActive ? "border-ring ring-ring/50 z-10 ring-[3px]" : "",
        props.error ? "border-destructive text-destructive" : "",
      )}
    >
      {props.char !== null && <div className="text-lg md:text-xl">{props.char}</div>}
    </div>
  );
}
