"use server";

import { auth } from "@/auth";
import { apiServer } from "@/lib/api-server";
import { ActionState } from "@/types/action.type";
import { tryCatch } from "@/utils/try-catch";

type VerifyOtpActionType = ActionState<string, string>;

export async function verifyOtp(
  state: VerifyOtpActionType,
  formData: FormData,
): Promise<VerifyOtpActionType> {
  try {
    const session = await auth();
    const payload = {
      type: formData.get("type") as "email" | "phone",
      otp: formData.get("otp") as string,
      attempt: formData.get("attempt") as string,
    };

    if (!session?.user) {
      return {
        success: false,
        message: "Unauthorized",
        errors: "Unauthorized",
        data: null,
      };
    }

    if (payload.type === "email") {
      const [res, err] = await tryCatch(
        apiServer({
          method: "POST",
          url: "/api/email/otp/verify/",
          body: JSON.stringify({ email: session.user.email, verification_code: payload.otp }),
        }),
      );

      if (err) {
        return {
          success: false,
          message: "Gagal verifikasi",
          errors: "Gagal verifikasi",
        };
      }

      const response = await res.json();
      console.log(response, payload);

      if (!res.ok) {
        return {
          success: false,
          message: `Gagal verifikasi email (Attempt: ${payload.attempt})`,
          errors: response.error as string,
          inputs: payload.attempt,
        };
      }

      return {
        success: true,
        message: "Berhasil verifikasi",
        data: response.message as string,
      };
    }

    if (payload.type === "phone") {
      const [res, err] = await tryCatch(
        apiServer({
          method: "POST",
          url: "/api/whatsapp/otp/verify/",
          body: JSON.stringify({
            phone_number: session.user.phone_number,
            verification_code: payload.otp,
          }),
        }),
      );

      if (err) {
        return {
          success: false,
          message: "Gagal verifikasi",
          errors: "Gagal verifikasi",
        };
      }

      const response = await res.json();
      console.log(response, payload);

      if (!res.ok) {
        return {
          success: false,
          message: `Gagal verifikasi nomor HP (Attempt: ${payload.attempt})`,
          errors: response.error as string,
          inputs: payload.attempt,
        };
      }

      return {
        success: true,
        message: "Berhasil verifikasi",
        data: response.message as string,
      };
    }

    return {
      success: false,
      message: "Tipe verifikasi tidak didukung",
      errors: "Tipe verifikasi tidak didukung",
      inputs: state.inputs,
      data: null,
    };
  } catch (error) {
    console.log(error);
    return {
      success: false,
      message: "Unexpected Error",
      errors: "Unexpected Error",
      inputs: state.inputs,
    };
  }
}
