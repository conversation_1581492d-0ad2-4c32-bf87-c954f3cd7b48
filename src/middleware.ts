import { NextResponse } from "next/server";

import { auth } from "./auth";

const protectAuth = ["/login", "/register"];

export default auth((req) => {
  const { nextUrl, auth } = req;

  if (nextUrl.pathname.startsWith("/dashboard/admin")) {
    const checkIsStaff = auth && auth.user.is_staff;

    if (!checkIsStaff) {
      return NextResponse.redirect(new URL("/dashboard", nextUrl));
    }
  }

  if (nextUrl.pathname.startsWith("/dashboard/interview")) {
    const isVerifiedEmail = auth && auth.user.email_verified;
    const isVerifiedPhone = auth && auth.user.phone_number_verified;
    
    if (!isVerifiedEmail || !isVerifiedPhone) {
      return NextResponse.redirect(new URL("/dashboard", nextUrl));
    }
  }

  if (nextUrl.pathname.startsWith("/dashboard")) {
    if (!auth?.user) {
      return NextResponse.redirect(new URL("/login", nextUrl));
    }

    if (auth.user) {
      if (!auth.user.is_onboarded) {
        return NextResponse.redirect(new URL("/onboarding", nextUrl));
      }
    }
  }

  if (nextUrl.pathname.startsWith("/onboarding")) {
    if (auth && auth.user.is_onboarded) {
      return NextResponse.redirect(new URL("/dashboard", nextUrl));
    }
  }

  if (protectAuth.includes(nextUrl.pathname) && auth?.user) {
    return NextResponse.redirect(new URL("/dashboard", nextUrl));
  }

  return NextResponse.next();
});

export const config = {
  matcher: ["/dashboard/:path*", "/verification/:path*", "/onboarding", "/login", "/register"],
};
