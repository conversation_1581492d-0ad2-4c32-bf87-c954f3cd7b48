"use server";

import qs from "qs";
import { cookies } from "next/headers";

import { APIProps } from "@/types/api.type";

export async function apiServer({ url, method, headers, body, params }: APIProps) {
  const cookiesStore = await cookies();
  const token = cookiesStore.get("token")?.value || "";
  const baseUrl = process.env.NEXT_PUBLIC_API_URL ?? "";

  const query = qs.stringify(params);

  return await fetch(baseUrl + url + (params ? `?${query}` : ""), {
    method: method ?? "GET",
    headers: {
      "Content-Type": "application/json",
      accept: "application/json",
      ...(token && { Authorization: `Token ${token}` }),
      ...headers,
    },
    body,
  });
}
