import qs from "qs";

export const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_URL || "http://localhost:1337";

export async function strapi({
  path,
  tags,
  params,
}: {
  path: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  params?: Record<string, any>;
  tags?: string[];
}) {
  const BASE_URL = STRAPI_URL;
  const queryString = qs.stringify(params);

  return await fetch(BASE_URL + path + (params ? `?${queryString}` : ""), {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      accept: "application/json",
      Authorization: `Bearer ${process.env.NEXT_PUBLIC_STRAPI_TOKEN}`,
    },
    cache: "no-store",
  });
}
