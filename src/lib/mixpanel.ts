export async function TrackEventMixpanel(eventName: string, properties: Record<string, unknown>) {
  await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/thirdparty/mixpanel`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      event: eventName,
      properties,
    }),
  });
}
export async function IdentifyUserMixpanel(email: string, properties: Record<string, unknown>) {
  await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/thirdparty/mixpanel/peopleset`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      email,
      properties,
    }),
  });
}
