import { cn } from "@/utils/cn";
import {
  <PERSON><PERSON><PERSON>_<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON>ont<PERSON><PERSON>,
  Inter,
} from "next/font/google";

const fontInter = Inter({ subsets: ["latin"], variable: "--font-inter" });
const fontSans = FontSans({ subsets: ["latin"], variable: "--font-sans" });
const fontMono = FontMono({
  subsets: ["latin"],
  variable: "--font-mono",
  weight: ["400"],
});


export const fontVariables = cn(
  fontSans.variable,
  fontMono.variable,
  fontInter.variable,
);
