@utility glassmorphism {
  position: relative;
  overflow: hidden;
  @apply shadow;

  &:before {
    content: " ";
    position: absolute;
    z-index: 0;
    inset: 0;

    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    filter: url(#glass-distortion);
    overflow: hidden;
    isolation: isolate;
  }

  &:after {
    content: "";
    position: absolute;
    z-index: 1;
    inset: 0;
    background-color: oklch(1 0 0 / 0.25);
  }
}

@utility strip {
  position: relative;

  &:after {
    content: "";
    position: absolute;
    height: 1px;
    width: 100%;
    inset: 0;
    z-index: 0;
    background: linear-gradient(
      90deg,
      transparent 0%,
      var(--border) 25%,
      var(--border) 75%,
      transparent 100%
    );
  }
}

@utility border-grid {
  @apply border-border/50 dark:border-border;
}

@utility theme-container {
  @apply font-sans;
}

@utility container {
  @apply 3xl:max-w-screen-2xl mx-auto max-w-[1400px];
}

@utility no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

@utility border-ghost {
  @apply after:border-border relative after:absolute after:inset-0 after:border after:mix-blend-darken dark:after:mix-blend-lighten;
}

@utility step-wrapper {
  counter-reset: step;
}

@utility step {
  position: relative;
  counter-increment: step;

  &:before {
    @apply text-muted-foreground bg-muted top-0 left-0 z-10 hidden size-7 items-center justify-center p-2 font-mono text-sm font-medium md:absolute md:flex;
    content: counter(step);
  }
}

@utility extend-touch-target {
  @media (pointer: coarse) {
    @apply relative touch-manipulation after:absolute after:-inset-2;
  }
}

@utility mask-gradient-opacity {
  mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 0) 55%, rgba(0, 0, 0, 1) 90%);
  -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 0) 5%, rgba(0, 0, 0, 1) 50%);
}

@utility bg-mesh-gradient {
  background-color: hsla(0, 0%, 100%, 1);
  background-image:
    radial-gradient(at 15% 41%, hsla(153, 69%, 69%, 0.5) 0px, transparent 50%),
    radial-gradient(at 80% 100%, hsla(241, 100%, 70%, 0.6) 0px, transparent 50%),
    radial-gradient(at 68% 88%, hsla(116, 70%, 70%, 0.5) 0px, transparent 50%),
    radial-gradient(at 96% 97%, hsla(66, 73%, 74%, 0.1) 0px, transparent 50%),
    radial-gradient(at 9% 62%, hsla(151, 100%, 70%, 0.5) 0px, transparent 50%),
    radial-gradient(at 0% 0%, hsla(343, 100%, 76%, 0.5) 0px, transparent 50%);
}

@utility bg-gradient-purple {
  background-image: linear-gradient(90deg, rgba(147, 51, 234, 1) 0%, rgba(219, 39, 119, 1) 100%);
}

@utility bg-gradient-green {
  background-image: linear-gradient(90deg, rgba(34, 197, 94, 1) 0%, rgba(59, 130, 246, 1) 100%);
}

@utility bg-gradient-blue {
  background-image: linear-gradient(
    90deg,
    oklch(71.375% 0.14347 254.643) 0%,
    oklch(58.541% 0.20415 277.126) 100%
  );
}

@utility bg-gradient-yellow {
  background-image: linear-gradient(90deg, rgba(250, 189, 54, 1) 0%, rgba(255, 193, 7, 1) 100%);
}

@utility bg-gradient-orange {
  background-image: linear-gradient(90deg, rgba(255, 101, 2, 1) 0%, rgba(255, 159, 0, 1) 100%);
}

@utility bg-gradient-red {
  background: linear-gradient(
    90deg,
    oklch(71.061% 0.16614 22.191) 0%,
    oklch(65.59% 0.21181 354.287) 100%
  );
}

@utility bg-gradient-silver {
  background: linear-gradient(
    90deg,
    oklch(0.9276 0.0058 264.53) 0%,
    oklch(0.8717 0.0093 258.34) 100%
  );
}

@utility bg-gradient-gold {
  background: linear-gradient(
    90deg,
    oklch(87.896% 0.15335 91.611) 0%,
    oklch(90.524% 0.16561 98.12) 100%
  );
}

@utility bg-rank-beginner {
  background: linear-gradient(90deg, #4ade80 0%, #22c55e 100%);
}

@utility bg-rank-master {
  background: linear-gradient(90deg, #60a5fa 0%, #3b82f6 100%);
}

@utility bg-rank-grandmaster {
  background: linear-gradient(90deg, #c084fc 0%, #a855f7 100%);
}

@utility bg-rank-epic {
  background: linear-gradient(90deg, #fb923c 0%, #eab308 100%);
}

@utility bg-rank-legend {
  background: linear-gradient(90deg, #ef4444 0%, #eab308 100%);
}

@utility bg-rank-mythic {
  background: linear-gradient(90deg, #facc15 0%, #ffffff 100%);
}
