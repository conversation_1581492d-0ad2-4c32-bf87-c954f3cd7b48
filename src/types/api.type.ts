export type APIProps =
  | {
      url: string;
      method?: "GET";
      headers?: HeadersInit;
      body?: never;
      cache?: RequestCache;
      next?: RequestInit["next"];
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      params?: Record<string, any>;
    }
  | {
      url: string;
      method?: "POST" | "PUT" | "PATCH" | "DELETE";
      headers?: HeadersInit;
      body?: BodyInit;
      cache?: RequestCache;
      next?: RequestInit["next"];
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      params?: Record<string, any>;
    };
