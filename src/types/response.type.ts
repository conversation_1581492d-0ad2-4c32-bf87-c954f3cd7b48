import { UserType } from "./auth.type";

export interface ICVScreeningReportResponse {
  file: string | null;
  id: number;
  full_name: string;
  position: string;
  score: number;
  format_and_structure_score: number;
  suitability_score: number;
  experiences_score: number;
  profile_summary_score: number;
  work_experience_score: number;
  education_score: number;
  skills_score: number;
  certifications_score: number;
  projects_score: number;
  achievements_score: number;
  summary: string;
  strengths: Array<string>;
  weaknesses: Array<string>;
  opportunities: Array<string>;
  threats: Array<string>;
  grammar: Array<{
    label: string;
    detail: string;
  }>;
  revisions: Array<{
    label: string;
    detail: string;
  }>;
}

export interface IGoogleAuthResponse {
  token: string;
  user: {
    pk: number;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    profile_picture: string;
  };
}

export interface IRegisterResponse {
  message: string;
  user: { username: string; email: string };
  token: string;
}

export interface ISchedule {
  id: number;
  start_time: string;
  end_time: string;
  booked_sessions: number;
  remaining_capacity: number;
}

export interface IScheduleResponse {
  status: number;
  message: string;
  date: string;
  start_time: string;
  end_time: string;
  posisi: string;
  jenis_wawancara: string;
  booking_code: string;
}

export interface IInterview {
  id: number;
  date: string;
  booking_code: string;
  tier: string;
  final_score: number | null;
  status: "Pending" | "Scheduled" | "Completed" | "Cancelled";
  tingkatan: "Entry" | "Mid" | "Senior" | "Lead" | "Manager";
  jenis_wawancara: string;
  posisi: string;
  industri: string;
  nama_perusahaan: string;
  detail_pekerjaan: string;
  skor_keseluruhan: 90;
  summary: string;
  domisili_saat_ini: string;
  kekuatan: string;
  kelemahan: string;
  tools: string;
  pendidikan: string;
  pengalaman_relevan: string;
  portofolio: string;
  sertifikasi: string;
  years_of_experience: number;
  user_profile: number;
  schedule: number;
}

export interface GladiaResponse {
  id: string;
  url: string;
}

export type HeygenInitiateSessionResponse = {
  message: string;
  token: string; // for send or stop task
  session_id: string; // for send or stop task
  livekit_connection: {
    server_url: string;
    token: string;
  };
};

export type InterviewUser = {
  username: string;
  email: string;
  full_name: string | null;
  profile_picture: string;
};

export type InterviewData = {
  id: number;
  final_score: number;
  date: string;
  booking_code: string;
  status: "Pending" | "Scheduled" | "Completed" | "Cancelled";
  tingkatan: string;
  jenis_wawancara: string;
  posisi: string;
  industri: string;
  nama_perusahaan: string;
  detail_pekerjaan: string;
  skor_keseluruhan: number;
  summary: string;
  domisili_saat_ini: string;
  kekuatan: string;
  kelemahan: string;
  tools: string;
  pendidikan: string;
  pengalaman_relevan: string;
  portofolio: string;
  sertifikasi: string;
  years_of_experience: number;
  user_profile: number;
  schedule: number;
  package: number;
};

export type InterviewResultDetail = {
  interview: number;
  final_score: number;
  final_summary: string;
  recommendation: string;
  strengths: string;
  gaps: string;
  communication_skills: string;
  cognitive_insights: string;
  multiple_faces: string;
  eye_contact: string;
  face_visibility: string;
  general_expression: string;
  camera_quality: string;
  camera_perspective: string;
  generated_at: string;
};

export type InterviewQuestion = {
  id: number;
  n8n_id: string;
  question: string;
  created_at: string;
  interview: number;
};

export type InterviewAnswer = {
  id: number;
  question_id: number;
  answer: string;
};

export type IInterviewResult = {
  user: InterviewUser;
  interview: InterviewData;
  result: InterviewResultDetail;
  questions: InterviewQuestion[];
  answers: InterviewAnswer[];
};

export type IDidResponse = {
  id: string;
  offer: {
    type: string;
    sdp: string;
  };
  ice_servers: Array<{
    urls: string[];
    username: string;
    credential: string;
  }>;
  session_id: string;
};

export type ISpeechMeticsResponse = { url: string; token: string };

export type IGetUsersResponse = {
  count: number;
  next: string | null;
  previous: string | null;
  results: Array<IGetUsersResult>;
};

export type IGetUsersResult = {
  id: number;
  username: string;
  user: number;
  email: string;
  full_name: string;
  phone_number: string;
  date_of_birth: string;
  gender: "Laki-laki" | "Perempuan";
  profile_picture: string;
  bio: string;
  service_api_key: string | null;
  phone_number_verified: boolean;
  email_verified: boolean;
  cv_file: string | null;
  created_at: string;
  is_staff: boolean;
  is_google: boolean;
  is_active: boolean;
};

export type GamificationMissionType = "DAILY" | "WEEKLY" | "MONTHLY" | "SPECIAL" | "GOAL";

export type GamificationMission = {
  id: number;
  name: string;
  description: string;
  mission_type: GamificationMissionType;
  completion_count: number;
  xp_reward: number;
  badge_reward: number | null;
  is_active: boolean;
  action: number;
};

export type GamificationUserMissions = {
  mission: {
    name: string;
    description: string;
    mission_type: GamificationMissionType;
    completion_count: number;
    xp_reward: number;
  };
  current_count: number;
  is_completed: boolean;
  action_name: GamificationAction;
};

export type GamificationBadge = {
  name: string;
  description: string;
  icon_url: string;
  earned_at: string | null;
};

export type GamificationRank = "Beginner" | "Master" | "Grandmaster" | "Epic" | "Legend" | "Mythic";
export type GamificationAction =
  | "finished_cv_builder"
  | "finished_cv_screening"
  | "finished_interview"
  | "login"
  | "onboarding_complete"
  | "onboarding_preparation_level"
  | "onboarding_primary_goal"
  | "onboarding_who_are_you";

export type GamificationLeaderboard = {
  leaderboard_rank: number;
  rank: GamificationRank;
  xp: number;
  level: number;
  user_profile: UserType;
};

export type GamificationProfile = {
  xp: number;
  level: number;
  rank: GamificationRank;
  user_profile: UserType;
};
