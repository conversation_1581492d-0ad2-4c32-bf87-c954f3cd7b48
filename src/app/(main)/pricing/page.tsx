import { Metadata } from "next";
import { notFound } from "next/navigation";

import { strapi } from "@/lib/strapi";
import FaqPricingView from "@/modules/pricing/view/faq.view";
import FeatureComparisonPricingView from "@/modules/pricing/view/feature-comparison.view";
import GuaranteePricingView from "@/modules/pricing/view/guarantee.view";
import HeroPricingView from "@/modules/pricing/view/hero.view";
import PackagePricingView from "@/modules/pricing/view/package.view";
import { StrapiPricingPageResponse } from "@/types/strapi.type";
import { tryCatch } from "@/utils/try-catch";

export const metadata: Metadata = {
  title: "Harga - Paket AI Interview Platform untuk Setiap Kebutuhan",
  description: "Pilih paket AI Interview yang sesuai dengan kebutuhan perusahaan Anda. Mulai dari paket gratis hingga enterprise dengan harga transparan dan tanpa biaya tersembunyi.",
  keywords: "harga AI interview, pricing recruitment platform, paket HR software, biaya interview online, subscription HR tools",
  openGraph: {
    title: "Harga Paket - AI Interview Platform",
    description: "Temukan paket AI Interview yang tepat untuk bisnis Anda dengan harga transparan dan fitur lengkap.",
    type: "website",
    locale: "id_ID",
    siteName: process.env.NEXT_PUBLIC_SITE_NAME,
    url: `${process.env.NEXT_PUBLIC_SITE_URL}/pricing`,
    images: [
      {
        url: `${process.env.NEXT_PUBLIC_SITE_URL}/og-pricing.jpg`,
        width: 1200,
        height: 630,
        alt: "Harga AI Interview Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Harga - AI Interview Platform",
    description: "Paket AI Interview untuk setiap kebutuhan dengan harga transparan.",
    images: [`${process.env.NEXT_PUBLIC_SITE_URL}/og-pricing.jpg`],
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_SITE_URL}/pricing`,
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default async function PricingPage() {
  const [res, err] = await tryCatch(strapi({ path: "/api/pricing-page" }));

  if (err || !res.ok) {
    notFound();
  }

  const response = (await res.json()) as StrapiPricingPageResponse;
  const data = response.data;

  const renderSections = () => {
    return data.sections.map((section, idx) => {
      switch (section.__component) {
        case "pricing-page.hero-section":
          return <HeroPricingView key={idx} data={section} />;
        case "pricing-page.pricing-section":
          return <PackagePricingView key={idx} data={section} />;
        case "pricing-page.guarantee-section":
          return <GuaranteePricingView key={idx} data={section} />;
        case "pricing-page.faq-section":
          return <FaqPricingView key={idx} data={section} />;
        case "pricing-page.feature-section":
          return <FeatureComparisonPricingView key={idx} data={section} />;
        default:
          return null;
      }
    });
  };

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    name: "Harga AI Interview Platform",
    description: "Daftar harga dan paket berlangganan AI Interview Platform.",
    url: `${process.env.NEXT_PUBLIC_SITE_URL}/pricing`,
    mainEntity: {
      "@type": "PriceSpecification",
      priceCurrency: "IDR",
    },
    offers: [
      {
        "@type": "Offer",
        name: "Paket Basic",
        price: "0",
        priceCurrency: "IDR",
        description: "Paket gratis untuk memulai dengan fitur dasar.",
      },
      {
        "@type": "Offer",
        name: "Paket Professional",
        price: "1500000",
        priceCurrency: "IDR",
        description: "Paket lengkap untuk tim HR profesional.",
      },
      {
        "@type": "Offer",
        name: "Paket Enterprise",
        price: "5000000",
        priceCurrency: "IDR",
        description: "Solusi custom untuk perusahaan besar.",
      },
    ],
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div className="flex flex-1 flex-col">{renderSections()}</div>
    </>
  );
}
