import { notFound } from "next/navigation";
import { Metadata } from "next";

import { strapi } from "@/lib/strapi";
import { HomeCommunityView } from "@/modules/home/<USER>/community.view";
import { HomeCtaView } from "@/modules/home/<USER>/cta.view";
import { HomeFaqView } from "@/modules/home/<USER>/faq.view";
import { HomeFeaturesView } from "@/modules/home/<USER>/features.view";
import { HomeHeroView } from "@/modules/home/<USER>/hero.view";
import { HomeInterfaceView } from "@/modules/home/<USER>/interface.view";
import { HomeJobView } from "@/modules/home/<USER>/job.view";
import { HomePricingView } from "@/modules/home/<USER>/pricing.view";
import { HomeProblemView } from "@/modules/home/<USER>/problem.view";
import { HomeStepsView } from "@/modules/home/<USER>/steps.view";
import { HomeTestimonialsView } from "@/modules/home/<USER>/testimonials.view";
import { StrapiHomePageResponse } from "@/types/strapi.type";
import { tryCatch } from "@/utils/try-catch";

export const metadata: Metadata = {
  title: "AI Interview Platform - Transformasi Rekrutmen dengan Kecerdasan Buatan",
  description: "Platform interview berbasis AI untuk screening CV otomatis, wawancara video real-time dengan avatar AI, dan analisis mendalam kandidat. Tingkatkan efisiensi rekrutmen hingga 80%.",
  keywords: "AI interview, screening CV, wawancara online, rekrutmen otomatis, HRD tools, talent acquisition, video interview platform",
  openGraph: {
    title: "AI Interview Platform - Transformasi Rekrutmen Digital",
    description: "Otomatisasi proses rekrutmen dengan AI: screening CV cerdas, wawancara video interaktif, dan analisis kandidat mendalam.",
    type: "website",
    locale: "id_ID",
    siteName: process.env.NEXT_PUBLIC_SITE_NAME,
    url: process.env.NEXT_PUBLIC_SITE_URL,
    images: [
      {
        url: `${process.env.NEXT_PUBLIC_SITE_URL}/og-image.jpg`,
        width: 1200,
        height: 630,
        alt: "AI Interview Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "AI Interview Platform - Transformasi Rekrutmen dengan AI",
    description: "Platform interview berbasis AI untuk screening CV otomatis dan wawancara video real-time.",
    images: [`${process.env.NEXT_PUBLIC_SITE_URL}/og-image.jpg`],
  },
  alternates: {
    canonical: process.env.NEXT_PUBLIC_SITE_URL,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default async function HomePage() {
  const [res, err] = await tryCatch(strapi({ path: "/api/home-page" }));

  if (err || !res.ok) {
    notFound();
  }

  const response = (await res.json()) as StrapiHomePageResponse;
  const data = response.data;

  const renderSections = () => {
    return data.sections.map((section, idx) => {
      switch (section.__component) {
        case "home-page.hero-section":
          return <HomeHeroView key={idx} data={section} />;
        case "home-page.job-section":
          return <HomeJobView key={idx} data={section} />;
        case "home-page.problem-section":
          return <HomeProblemView key={idx} data={section} />;
        case "home-page.tutotial-section":
          return <HomeStepsView key={idx} data={section} />;
        case "home-page.feature-section":
          return <HomeFeaturesView key={idx} data={section} />;
        case "home-page.interface-section":
          return <HomeInterfaceView key={idx} data={section} />;
        case "home-page.community-section":
          return <HomeCommunityView key={idx} data={section} />;
        case "home-page.testimoni-section":
          return <HomeTestimonialsView key={idx} data={section} />;
        case "home-page.pricing-section":
          return <HomePricingView key={idx} data={section} />;
        case "home-page.faq-section":
          return <HomeFaqView key={idx} data={section} />;
        case "home-page.cta-section":
          return <HomeCtaView key={idx} data={section} />;
        default:
          return null;
      }
    });
  };

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    name: process.env.NEXT_PUBLIC_SITE_NAME,
    url: process.env.NEXT_PUBLIC_SITE_URL,
    description: "Platform interview berbasis AI untuk screening CV otomatis, wawancara video real-time dengan avatar AI, dan analisis mendalam kandidat.",
    applicationCategory: "BusinessApplication",
    operatingSystem: "Web",
    offers: {
      "@type": "AggregateOffer",
      priceCurrency: "IDR",
      lowPrice: "0",
      highPrice: "5000000",
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.8",
      reviewCount: "250",
    },
    provider: {
      "@type": "Organization",
      name: process.env.NEXT_PUBLIC_SITE_NAME,
      url: process.env.NEXT_PUBLIC_SITE_URL,
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div className="flex flex-1 flex-col bg-background">{renderSections()}</div>
    </>
  );
}
