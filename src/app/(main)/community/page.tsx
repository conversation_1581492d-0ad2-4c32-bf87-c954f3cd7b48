import { Metadata } from "next";
import { notFound } from "next/navigation";

import DiscordServerCommunityView from "@/modules/community/view/discord.view";
import GuidelinesCommunityView from "@/modules/community/view/guidelines.view";
import WhatsAppCommunityView from "@/modules/community/view/whatsapp.view";
import { CommunityHeroView } from "@/modules/community/view/hero.view";

import { StrapiCommunityPageResponse } from "@/types/strapi.type";
import { strapi } from "@/lib/strapi";
import { tryCatch } from "@/utils/try-catch";

export const metadata: Metadata = {
  title: "Komunitas AI Interview - Bergabung dengan Profesional HR Indonesia",
  description: "Bergabung dengan komunitas HR profesional Indonesia. Diskusi seputar AI recruitment, best practices interview, dan tips optimasi proses rekrutmen di Discord dan WhatsApp.",
  keywords: "komunitas HR, AI recruitment community, HR Indonesia, diskusi rekrutmen, HR networking, interview tips, talent acquisition community",
  openGraph: {
    title: "Komunitas AI Interview Platform",
    description: "Bergabung dengan ribuan HR profesional Indonesia dalam komunitas AI Interview. Dapatkan tips, trik, dan best practices rekrutmen.",
    type: "website",
    locale: "id_ID",
    siteName: process.env.NEXT_PUBLIC_SITE_NAME,
    url: `${process.env.NEXT_PUBLIC_SITE_URL}/community`,
    images: [
      {
        url: `${process.env.NEXT_PUBLIC_SITE_URL}/og-community.jpg`,
        width: 1200,
        height: 630,
        alt: "Komunitas AI Interview Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Komunitas AI Interview - HR Profesional Indonesia",
    description: "Bergabung dengan komunitas HR profesional untuk diskusi AI recruitment dan best practices.",
    images: [`${process.env.NEXT_PUBLIC_SITE_URL}/og-community.jpg`],
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_SITE_URL}/community`,
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default async function CommunityPage() {
  const [res, err] = await tryCatch(strapi({ path: "/api/community-page" }));

  if (err || !res.ok) {
    notFound();
  }

  const response = (await res.json()) as StrapiCommunityPageResponse;
  const data = response.data;

  const renderSections = () => {
    return data.sections.map((section, idx) => {
      switch (section.__component) {
        case "community-page.hero-section":
          return <CommunityHeroView key={idx} data={section} />;
        case "community-page.whats-app-section":
          return <WhatsAppCommunityView key={idx} data={section} />;
        case "community-page.discord-section":
          return <DiscordServerCommunityView key={idx} data={section} />;
        case "community-page.guideline-section":
          return <GuidelinesCommunityView key={idx} data={section} />;
        default:
          return null;
      }
    });
  };

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    name: "Komunitas AI Interview Platform",
    description: "Bergabung dengan komunitas HR profesional Indonesia untuk diskusi AI recruitment.",
    url: `${process.env.NEXT_PUBLIC_SITE_URL}/community`,
    mainEntity: {
      "@type": "Organization",
      name: "AI Interview Community",
      description: "Komunitas HR profesional Indonesia yang fokus pada AI recruitment dan interview best practices.",
      url: `${process.env.NEXT_PUBLIC_SITE_URL}/community`,
      sameAs: [
        "https://discord.gg/your-server",
        "https://chat.whatsapp.com/your-group",
      ],
      memberOf: {
        "@type": "ProfessionalService",
        name: process.env.NEXT_PUBLIC_SITE_NAME,
      },
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div className="flex flex-1 flex-col">{renderSections()}</div>
    </>
  );
}
