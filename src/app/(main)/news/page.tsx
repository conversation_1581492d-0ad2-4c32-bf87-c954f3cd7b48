import qs from "qs";
import { Metadata } from "next";
import { notFound } from "next/navigation";

import ArticleNewsView from "@/modules/news/views/artikel.view";
import HeadlinesNewsView from "@/modules/news/views/headlines.view";
import HeroNewsView from "@/modules/news/views/hero.view";
import NewsletterNewsView from "@/modules/news/views/newsletter.view";

import { strapi } from "@/lib/strapi";
import { tryCatch } from "@/utils/try-catch";
import { StrapiNewsArticle, StrapiNewsPageResponse } from "@/types/strapi.type";

export const metadata: Metadata = {
  title: "Berita & Artikel - Update Terbaru AI Recruitment dan HR Tech",
  description:
    "Baca artikel terbaru tentang tren AI recruitment, HR technology, tips interview, dan best practices dalam dunia rekrutmen digital.",
  keywords:
    "berita HR, AI recruitment news, HR technology, artikel rekrutmen, tren interview, HR insights, recruitment tips",
  openGraph: {
    title: "<PERSON>rita & Artikel - AI Interview Platform",
    description: "Update terbaru dan insight mendalam tentang AI recruitment dan HR technology.",
    type: "website",
    locale: "id_ID",
    siteName: process.env.NEXT_PUBLIC_SITE_NAME,
    url: `${process.env.NEXT_PUBLIC_SITE_URL}/news`,
    images: [
      {
        url: `${process.env.NEXT_PUBLIC_SITE_URL}/og-news.jpg`,
        width: 1200,
        height: 630,
        alt: "Berita AI Interview Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Berita & Artikel - AI Interview Platform",
    description: "Update terbaru tentang AI recruitment dan HR technology.",
    images: [`${process.env.NEXT_PUBLIC_SITE_URL}/og-news.jpg`],
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_SITE_URL}/news`,
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default async function NewsPage() {
  const [res, err] = await tryCatch(strapi({ path: "/api/news-page" }));
  const newsResponse = await strapi({
    path: "/api/news-articles",
    params: {
      sort: ["publishedAt:desc", "createdAt:desc"],
      pagination: {
        pageSize: 4,
        page: 1,
      },
    },
  });

  if (err || !res.ok || !newsResponse.ok) {
    notFound();
  }

  const [response, newsResponseData] = await Promise.all([res.json(), newsResponse.json()]);

  const data = response.data as StrapiNewsPageResponse["data"];
  const news = newsResponseData.data as StrapiNewsArticle[] | null;

  const renderSections = () => {
    return data.sections.map((section, idx) => {
      switch (section.__component) {
        case "news-page.hero-section":
          return <HeroNewsView key={idx} data={section} />;
        case "news-page.headline-section":
          return <HeadlinesNewsView key={idx} data={section} news={news} />;
        case "news-page.article-section":
          return <ArticleNewsView key={idx} data={section} />;
        case "news-page.newsletter-section":
          return <NewsletterNewsView key={idx} data={section} />;
        default:
          return null;
      }
    });
  };

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    name: "Berita & Artikel AI Interview Platform",
    description: "Kumpulan artikel dan berita terbaru tentang AI recruitment dan HR technology.",
    url: `${process.env.NEXT_PUBLIC_SITE_URL}/news`,
    mainEntity: {
      "@type": "ItemList",
      name: "Daftar Artikel",
      description: "Artikel terbaru tentang AI recruitment dan HR technology",
    },
    publisher: {
      "@type": "Organization",
      name: process.env.NEXT_PUBLIC_SITE_NAME,
      url: process.env.NEXT_PUBLIC_SITE_URL,
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div className="flex flex-1 flex-col">{renderSections()}</div>
    </>
  );
}
