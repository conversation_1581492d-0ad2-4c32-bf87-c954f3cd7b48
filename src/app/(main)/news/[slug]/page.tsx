import { Suspense } from "react";
import { Metadata } from "next";
import { notFound } from "next/navigation";

import { DetailNewsView } from "@/modules/news/views/detail.view";
import { RelatedNewsView } from "@/modules/news/views/related-news.view";

import { StrapiNewsArticle, StrapiResponse } from "@/types/strapi.type";
import { strapi } from "@/lib/strapi";
import { tryCatch } from "@/utils/try-catch";
import { getRelatedNews } from "@/modules/news/services/get-related-news.service";
import { InterviewCard } from "@/modules/news/components/interview-card";
import { getImageUrl } from "@/utils/image-url";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params;
  const [res, err] = await tryCatch(
    strapi({ path: "/api/news-articles?filters[slug][$eq]=" + slug }),
  );

  if (err || !res.ok) {
    return {
      title: "Artikel Tidak Ditemukan",
    };
  }

  const response = (await res.json()) as StrapiResponse<StrapiNewsArticle[]>;
  const data = response.data[0];

  if (!data) {
    return {
      title: "Artikel Tidak Ditemukan",
    };
  }

  return {
    title: `${data.title} - AI Interview Platform`,
    description:
      data.description || "Baca artikel lengkap tentang AI recruitment dan HR technology.",
    // keywords: data.tags?.join(", ") || "AI interview, HR technology, recruitment",
    openGraph: {
      title: data.title,
      description: data.description,
      type: "article",
      locale: "id_ID",
      siteName: process.env.NEXT_PUBLIC_SITE_NAME,
      url: `${process.env.NEXT_PUBLIC_SITE_URL}/news/${slug}`,
      images: data.thumbnail?.url
        ? [
            {
              url: getImageUrl(data.thumbnail.url, "strapi"),
              width: 1200,
              height: 630,
              alt: data.title,
            },
          ]
        : [],
      publishedTime: data.publishedAt,
      modifiedTime: data.updatedAt,
    },
    twitter: {
      card: "summary_large_image",
      title: data.title,
      description: data.description,
      images: data.thumbnail?.url ? [getImageUrl(data.thumbnail.url, "strapi")] : [],
    },
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_SITE_URL}/news/${slug}`,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function NewsDetailPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  const [res, err] = await tryCatch(
    strapi({ path: `/api/news-articles`, params: { filters: { slug: { $eq: slug } } } }),
  );

  if (err || !res.ok) {
    notFound();
  }

  const response = (await res.json()) as StrapiResponse<StrapiNewsArticle[]>;
  const data = response.data[0];

  if (!data) {
    notFound();
  }

  const relatedNews = getRelatedNews(slug, data.news_category.slug);

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "NewsArticle",
    headline: data.title,
    description: data.description,
    image: data.thumbnail?.url ? getImageUrl(data.thumbnail.url, "strapi") : undefined,
    datePublished: data.publishedAt,
    dateModified: data.updatedAt,
    author: {
      "@type": "Organization",
      name: "AI Interview Platform",
    },
    publisher: {
      "@type": "Organization",
      name: process.env.NEXT_PUBLIC_SITE_NAME,
      url: process.env.NEXT_PUBLIC_SITE_URL,
    },
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": `${process.env.NEXT_PUBLIC_SITE_URL}/news/${slug}`,
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div className="bg-muted min-h-screen w-full px-4">
        <DetailNewsView data={data} />

        <InterviewCard />

        {/* Related News */}
        <Suspense fallback={<div>Loading...</div>}>
          <RelatedNewsView promise={relatedNews} />
        </Suspense>
      </div>
    </>
  );
}
