import { Metadata } from "next";
import { notFound } from "next/navigation";

import { strapi } from "@/lib/strapi";
import ContactFaqView from "@/modules/faq/views/contact.view";
import FaqSectionView from "@/modules/faq/views/faq-section.view";
import FaqHeroView from "@/modules/faq/views/hero.view";
import { StrapiFaqPageResponse } from "@/types/strapi.type";
import { tryCatch } from "@/utils/try-catch";

export const metadata: Metadata = {
  title: "FAQ - Pertanyaan Umum tentang AI Interview Platform",
  description: "Temukan jawaban atas pertanyaan umum seputar AI Interview Platform. Panduan lengkap tentang fitur, harga, integrasi, dan dukungan teknis.",
  keywords: "FAQ AI Interview, pertanyaan umum, bantuan HR, panduan interview, technical support, customer service",
  openGraph: {
    title: "FAQ - AI Interview Platform",
    description: "Pertanyaan umum dan panduan lengkap menggunakan AI Interview Platform untuk rekrutmen.",
    type: "website",
    locale: "id_ID",
    siteName: process.env.NEXT_PUBLIC_SITE_NAME,
    url: `${process.env.NEXT_PUBLIC_SITE_URL}/faq`,
    images: [
      {
        url: `${process.env.NEXT_PUBLIC_SITE_URL}/og-faq.jpg`,
        width: 1200,
        height: 630,
        alt: "FAQ AI Interview Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "FAQ - AI Interview Platform",
    description: "Pertanyaan umum dan panduan lengkap menggunakan AI Interview Platform.",
    images: [`${process.env.NEXT_PUBLIC_SITE_URL}/og-faq.jpg`],
  },
  alternates: {
    canonical: `${process.env.NEXT_PUBLIC_SITE_URL}/faq`,
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default async function FaqPage() {
  const [res, err] = await tryCatch(strapi({ path: "/api/faq-page" }));

  if (err || !res.ok) {
    notFound();
  }

  const response = (await res.json()) as StrapiFaqPageResponse;
  const data = response.data;

  const renderSections = () => {
    return data.sections.map((section, idx) => {
      switch (section.__component) {
        case "faq-page.hero-section":
          return <FaqHeroView key={idx} data={section} />;
        case "faq-page.faq-section":
          return <FaqSectionView key={idx} data={section} />;
        case "faq-page.contact-section":
          return <ContactFaqView key={idx} data={section} />;
        default:
          return null;
      }
    });
  };

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    name: "FAQ AI Interview Platform",
    description: "Pertanyaan umum tentang AI Interview Platform",
    url: `${process.env.NEXT_PUBLIC_SITE_URL}/faq`,
    mainEntity: [
      {
        "@type": "Question",
        name: "Apa itu AI Interview Platform?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "AI Interview Platform adalah solusi rekrutmen berbasis AI yang membantu HR melakukan screening CV dan wawancara otomatis.",
        },
      },
      {
        "@type": "Question",
        name: "Bagaimana cara kerja AI Interview?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "Platform kami menggunakan AI untuk screening CV, melakukan wawancara video dengan avatar AI, dan memberikan analisis kandidat secara otomatis.",
        },
      },
      {
        "@type": "Question",
        name: "Apakah ada free trial?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "Ya, kami menyediakan free trial 14 hari dengan akses penuh ke semua fitur.",
        },
      },
    ],
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div className="flex flex-1 flex-col">{renderSections()}</div>
    </>
  );
}
