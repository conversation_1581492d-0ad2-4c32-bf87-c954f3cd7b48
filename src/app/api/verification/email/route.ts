import { auth } from "@/auth";
import { apiServer } from "@/lib/api-server";
import { apiRouteResponse } from "@/utils/api-route-response";
import { tryCatch } from "@/utils/try-catch";

export async function POST() {
  const session = await auth();

  if (!session?.user.email) {
    return apiRouteResponse(null, false, "No email provided", 400);
  }

  const [res, err] = await tryCatch(
    apiServer({
      method: "POST",
      url: "/api/email/otp/",
      body: JSON.stringify({ email: session.user.email }),
    }),
  );

  if (err || !res.ok) {
    const response = await res?.json();
    console.log(response);
    return apiRouteResponse(null, false, "Failed to send OTP", 500);
  }

  const data = await res.json();
  console.log(data);

  return apiRouteResponse(data, true, "success", 200);
}
