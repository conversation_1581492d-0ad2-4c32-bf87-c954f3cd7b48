import { auth } from "@/auth";
import { apiServer } from "@/lib/api-server";
import { apiRouteResponse } from "@/utils/api-route-response";
import { tryCatch } from "@/utils/try-catch";

export async function POST() {
  const session = await auth();

  if (!session?.user.email) {
    return apiRouteResponse(null, false, "No email provided", 400);
  }

  const [res, err] = await tryCatch(
    apiServer({
      method: "POST",
      url: "/api/whatsapp/otp/",
      body: JSON.stringify({ phone_number: session.user.phone_number }),
    }),
  );

  if (err || !res.ok) {
    const response = await res?.json();
    console.log(response);
    return apiRouteResponse(null, false, "Failed to send OTP", 500);
  }

  const data = await res.json();
  console.log(data);

  return apiRouteResponse(data, true, "success", 200);
}
