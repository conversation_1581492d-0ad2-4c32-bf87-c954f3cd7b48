import { revalidatePath } from "next/cache";

import { apiServer } from "@/lib/api-server";
import { apiRouteResponse } from "@/utils/api-route-response";

export async function POST(req: Request, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const token = req.headers.get("Authorization")?.split(" ")[1];

  if (!token) {
    return apiRouteResponse(null, false, "Unauthorized", 401);
  }

  revalidatePath("/dashboard/cv-screening/" + id);
  const payload = { id, token, reqUrl: req.url };
  console.log(payload);
  return apiRouteResponse(payload, true, "success", 200);
}

export async function GET(req: Request, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;

  const cv = apiServer({
    method: "GET",
    url: "/api/cv-screening/report/" + id + "/build/store",
  });

  const res = await cv;
  const blob = await res.blob();
  const file = new File([blob], "cv.pdf", { type: "application/pdf" });

  return new Response(file);
}
