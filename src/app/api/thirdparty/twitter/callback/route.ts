import { NextResponse } from "next/server";

import { auth } from "@/auth";
import { TrackEventMixpanel } from "@/lib/mixpanel";
import { getResult } from "@/modules/interview/services/get-result.service";

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const code = searchParams.get("code");
  const id = searchParams.get("state")

  const cookies = req.headers.get("cookie") ?? "";
  const match = cookies.match(/twitter_code_verifier=([^;]+)/);
  const codeVerifier = match ? match[1] : null;

  const clientID = process.env.TWITTER_CLIENT_ID
  const secretID = process.env.TWITTER_SECRET_ID
  const baseUrl = process.env.NEXT_PUBLIC_API_URL
    
  if (!code || !id || !clientID || !secretID) {
    return NextResponse.json({ error: "No code found" }, { status: 400 });
  }

  try {
    const tokenRes = await fetch("https://api.twitter.com/2/oauth2/token", {
    method: "POST",
    headers: { 
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": `Basic ${Buffer.from(
      `${clientID}:${secretID}`  
        ).toString("base64")}`
        },
    body: new URLSearchParams({
      grant_type: "authorization_code",
      code,
      redirect_uri: `${baseUrl}/api/thirdparty/twitter/callback`,
      code_verifier: codeVerifier ? codeVerifier : "", 
     }),
    });

    if (!tokenRes.ok) {
      const errBody = await tokenRes.json()
      throw new Error(errBody.message || JSON.stringify(errBody))
    }
  
    const tokenData = await tokenRes.json();
    const accessToken = tokenData.access_token;

    const data = (await getResult(id))?.result
    if (!data) {
      throw new Error("Data is not found")
    }

    const tweetRes = await fetch("https://api.twitter.com/2/tweets", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        // 280 caracter only if not premium account
        text: data.recommendation,
      }),
    });
 
    if (!tweetRes.ok) {
    const errBody = await tweetRes.json()
    console.log(errBody)
    throw new Error(errBody.message || JSON.stringify(errBody))
    }

    const session = await auth()
    TrackEventMixpanel("user_share_result_interview", {
          distinct_id : session?.user.email,
          share_to : "Twitter",
          time_share : new Date().toLocaleString("en-US", { timeZone: "Asia/Jakarta" })
    })

    return NextResponse.redirect(new URL(`${baseUrl}/dashboard/interview/${id}/detail?status=success`))
  } catch(error) {
    console.error(error)
    return NextResponse.redirect(new URL(`${baseUrl}/dashboard/interview/${id}/detail?status=failed`))
  }
}
