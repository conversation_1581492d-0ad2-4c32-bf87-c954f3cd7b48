import { NextResponse } from "next/server";

import { getResult } from "@/modules/interview/services/get-result.service";
import { auth } from "@/auth";
import { TrackEventMixpanel } from "@/lib/mixpanel";

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const code = searchParams.get("code");
  const id = searchParams.get("state");

  const clientId = process.env.LINKEDIN_CLIENT_ID;
  const clientSecret = process.env.LINKEDIN_CLIENT_SECRET;
  const baseUrl = process.env.NEXT_PUBLIC_API_URL;

  if (!code || !clientId || !clientSecret || !id) {
    throw new Error("Bad Request");
  }

  try {
    const tokenRes = await fetch("https://www.linkedin.com/oauth/v2/accessToken", {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body: new URLSearchParams({
        grant_type: "authorization_code",
        code,
        redirect_uri: `${baseUrl}/api/thirdparty/linkedin/callback`,
        client_id: clientId,
        client_secret: clientSecret,
      }),
    });
    if (!tokenRes.ok) {
      const errBody = await tokenRes.json();
      throw new Error(errBody.message || JSON.stringify(errBody));
    }

    const tokenData = await tokenRes.json();
    const accessToken = tokenData.access_token;

    const meRes = await fetch("https://api.linkedin.com/v2/userinfo", {
      headers: { Authorization: `Bearer ${accessToken}` },
    });
    if (!meRes.ok) {
      const errBody = await meRes.json();
      throw new Error(errBody.message || JSON.stringify(errBody));
    }

    const me = await meRes.json();
    const authorUrn = `urn:li:person:${me.sub}`;

    const data = (await getResult(id))?.result;
    if (!data) {
      throw new Error("Data is not found");
    }

    // const [res, err] = await tryCatch(strapi({ path: "/api/share-result-interview" }));

    // if (err || !res.ok) {
    //   return null
    // }

    // const result = await res.json()
    // console.log(result)

    const postRes = await fetch("https://api.linkedin.com/v2/ugcPosts", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
        "X-Restli-Protocol-Version": "2.0.0",
      },
      body: JSON.stringify({
        author: authorUrn,
        lifecycleState: "PUBLISHED",
        specificContent: {
          "com.linkedin.ugc.ShareContent": {
            shareCommentary: { text: data.final_summary },
            shareMediaCategory: "NONE",
          },
        },
        visibility: {
          "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC",
        },
      }),
    });
    if (!postRes.ok) {
      const errBody = await postRes.json();
      throw new Error(errBody.message || JSON.stringify(errBody));
    }

    const session = await auth();
    TrackEventMixpanel("user_share_result_interview", {
      distinct_id: session?.user.email,
      share_to: "Linkedin",
      time_share: new Date().toLocaleString("en-US", { timeZone: "Asia/Jakarta" }),
    });

    return NextResponse.redirect(
      new URL(`${baseUrl}/dashboard/interview/${id}/detail?status=success`),
    );
  } catch (error) {
    console.error(error);
    return NextResponse.redirect(
      new URL(`${baseUrl}/dashboard/interview/${id}/detail?status=failed`),
    );
  }
}
