import { NextResponse } from "next/server";
import Mixpanel from "mixpanel"

const mixpanel = Mixpanel.init(process.env.NEXT_MIXPANEL_TOKEN!, {
    protocol : "https"
});

export async function POST(request: Request) {
  const data = await request.json();
  try {
    const { email, properties } = data;
    
    mixpanel.people.set(email, properties)

    return NextResponse.json({ status: "Event tracked successfully" });
  } catch (error) {
    console.log(error);
    return NextResponse.json(
      { error },
      { status: 500 }
    );
  }
}