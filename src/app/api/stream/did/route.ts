import { getDid } from "@/modules/interview/services/did.service";
import { apiRouteResponse } from "@/utils/api-route-response";

type BodyType = "init" | "offer";

const DID_API = {
  key: "********************************:MXKQrFTogSL4Z8vg3b4Dm",
  url: "https://api.d-id.com",
  websocketUrl: "wss://ws-api.d-id.com",
  service: "clips",
};

export async function POST(request: Request) {
  const body = (await request.json()) as { type: BodyType; apiKey: string };

  switch (body.type) {
    case "init":
      const did = await getDid(body.apiKey);

      if (!did) {
        return apiRouteResponse(null, false, "Failed to get DID");
      }

      return apiRouteResponse({ did, api: DID_API });
    default:
      return apiRouteResponse(null, false, "Invalid type", 500);
  }
}
