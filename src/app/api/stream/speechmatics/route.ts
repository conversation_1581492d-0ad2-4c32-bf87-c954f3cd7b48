import { getSpeechmatics } from "@/modules/interview/services/speechmatics.service";
import { apiRouteResponse } from "@/utils/api-route-response";

export async function POST(request: Request) {
  const body = (await request.json()) as { apiKey: string };
  const referer = request.headers.get("referer");
  const bookingCode = referer?.split("/").pop() || "";

  const speechmatics = await getSpeechmatics(body.apiKey, bookingCode);

  if (!speechmatics) {
    return apiRouteResponse(null, false, "Failed to get Speechmatics");
  }

  return apiRouteResponse(speechmatics);
}
