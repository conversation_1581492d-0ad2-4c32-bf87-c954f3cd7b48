import { notFound } from "next/navigation";

import { getResult } from "@/modules/interview/services/get-result.service";

import { InterviewSessionCards } from "@/modules/interview/components/interview-session-detail/card.interview-session-detail";
import { InterviewSessionProfile } from "@/modules/interview/components/interview-session-detail/profile.interview-session-detail";
import { InterviewSessionHeader } from "@/modules/interview/components/interview-session-detail/header.interview-session-detail";
import { InterviewSessionResultAssessment } from "@/modules/interview/components/interview-session-detail/result-assessment.interview-session-detail";
import { InterviewSessionTechnicalAssessment } from "@/modules/interview/components/interview-session-detail/technical-assessment.interview-session-detail";
import { InterviewSessionVideoAssessment } from "@/modules/interview/components/interview-session-detail/video-assessment.interview-session-detail";
import { InterviewSessionSummary } from "@/modules/interview/components/interview-session-detail/summary.interview-session-detail";
import { InterviewSessionQna } from "@/modules/interview/components/interview-session-detail/qna-interview-session-detail";

export default async function InterviewDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const data = await getResult(id);

  if (!data) {
    notFound();
  }

  return (
    <div className="@container/interview-detail relative flex w-full flex-1 flex-col">
      <InterviewSessionHeader id={id} data={data} />

      <div className="p-2 md:p-4">
        <div className="grid grid-cols-1 gap-6 @6xl/interview-detail:grid-cols-6">
          <InterviewSessionCards
            data={data}
            className="grid w-full grid-cols-1 gap-4 @6xl/interview-detail:col-span-full @6xl/interview-detail:grid-cols-4 @6xl/interview-detail:gap-6"
          />

          <div className="col-span-full grid grid-cols-subgrid gap-6">
            <InterviewSessionProfile
              data={data}
              className="top-[calc(var(--header-height)+1rem)] h-fit @6xl/interview-detail:sticky @6xl/interview-detail:col-span-2"
            />

            <div className="space-y-6 @6xl/interview-detail:col-span-4">
              <InterviewSessionResultAssessment data={data} />
              <InterviewSessionTechnicalAssessment data={data} />
              <InterviewSessionVideoAssessment data={data} />
              <InterviewSessionSummary data={data} />
              <InterviewSessionQna data={data} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
