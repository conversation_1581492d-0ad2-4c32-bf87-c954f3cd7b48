import { auth } from "@/auth";
import { apiWebSocket } from "@/lib/api-websocket";
import { getDeviceInfo } from "@/lib/device-detection";
import { InterviewView } from "@/modules/interview/views/interview.view";
import { QueueInterviewView } from "@/modules/interview/views/queue-interview.view";
import { tryCatch } from "@/utils/try-catch";

export const revalidate = 30;

export default async function InterviewPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const { isMobile, isTablet } = await getDeviceInfo();
  const session = await auth();

  const [res, err] = await tryCatch(
    apiWebSocket({
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      url: "/api/interview/queue/",
      body: JSON.stringify({ booking_code: id }),
      apiKey: session?.user.service_api_key || "",
      cache: "no-store",
    }),
  );

  if (err) {
    console.log(err);
  }

  const response = (await res?.json()) as { url: string };

  if (response.url) {
    return <QueueInterviewView url={response.url} />;
  }

  return <InterviewView sessionID={id} isMobile={isMobile || isTablet} />;
}
