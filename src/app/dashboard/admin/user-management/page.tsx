import { getDeviceInfo } from "@/lib/device-detection";
import { querySchema, QuerySchemaType } from "@/modules/admin/schemas/query.schema";
import { UserManagementView } from "@/modules/admin/views/user-management.view";
import { DashboardHeaderView } from "@/modules/dashboard/views/header.view";
import { notFound } from "next/navigation";

export default async function AdminUserManagementPage({
  searchParams,
}: {
  searchParams: Promise<QuerySchemaType>;
}) {
  const rawQuery = await searchParams;
  const validateQuery = await querySchema.safeParseAsync(rawQuery);
  const { isDesktop } = await getDeviceInfo();

  if (!validateQuery.success) {
    notFound();
  }

  const query = validateQuery.data;

  if (isDesktop && !query.limit) {
    query.limit = 25;
  }

  return (
    <div className="@container/admin-user-management size-full">
      <DashboardHeaderView
        title="User Management"
        description="User Management adalah fitur untuk mengelola data pengguna dalam sistem. Admin dapat menambahkan, mengubah, menonaktifkan, atau menghapus akun pengguna sesuai kebutuhan."
      >
        <div className="text-muted-foreground bg-muted space-y-0.5 self-end rounded-md border px-4 py-2 leading-none uppercase shadow-sm">
          <div className="flex items-center gap-2 pr-4 text-xs font-semibold md:text-base">
            <div className="bg-primary/5 border-primary h-[4px] w-3 rounded-full border md:w-6" />
            Admin
          </div>
          <div className="flex items-center gap-2 pr-4 text-xs font-semibold md:text-base">
            <div className="bg-muted/10 border-foreground/30 h-[4px] w-3 rounded-full border md:w-6" />
            User
          </div>
        </div>
      </DashboardHeaderView>

      <UserManagementView query={query} isDesktop={isDesktop} />
    </div>
  );
}
