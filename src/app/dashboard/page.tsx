import { DashboardSessionView } from "@/modules/dashboard/views/session.view";
import { DashboardCVHistoryView } from "@/modules/dashboard/views/cv-history.view";
import { GamificationMissionView } from "@/modules/gamification/views/mission.view";
import { GamificationBadgeView } from "@/modules/gamification/views/badge.view";
import { GamificationLeaderboardView } from "@/modules/gamification/views/leaderboard.view";
import { GamificationProfileView } from "@/modules/gamification/views/profile.view";

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default async function DashboardPage({
  searchParams,
}: {
  searchParams: Promise<{ tab: string }>;
}) {
  const { tab } = await searchParams;

  return (
    <>
      <GamificationProfileView />

      <Tabs defaultValue={tab || "interview"} className="gap-0">
        <TabsList className="text-foreground bg-background h-auto w-full rounded-none border-b px-0 py-1">
          <TabsTrigger
            value="interview"
            className="hover:bg-accent hover:text-foreground data-[state=active]:after:bg-gradient-purple data-[state=active]:hover:bg-accent relative after:absolute after:inset-x-0 after:bottom-0 after:-mb-1 after:h-0.5 data-[state=active]:bg-transparent data-[state=active]:shadow-none"
          >
            Interview & CV Screening
          </TabsTrigger>
          <TabsTrigger
            value="mission"
            className="hover:bg-accent hover:text-foreground data-[state=active]:after:bg-gradient-purple data-[state=active]:hover:bg-accent relative after:absolute after:inset-x-0 after:bottom-0 after:-mb-1 after:h-0.5 data-[state=active]:bg-transparent data-[state=active]:shadow-none"
          >
            Achievement & Ranking
          </TabsTrigger>
        </TabsList>

        <TabsContent value="mission" key="mission">
          <div className="container mx-auto flex flex-col gap-6 p-0 sm:p-2 md:p-4">
            <div className="grid sm:gap-6 @lg/dashboard:grid-cols-2">
              <GamificationMissionView />

              <GamificationBadgeView />

              <GamificationLeaderboardView />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="interview" key="interview">
          <div className="grid grid-cols-1 gap-0 sm:gap-6 p-0 sm:p-2 md:p-4 @[90rem]/dashboard:grid-cols-2">
            <DashboardSessionView />
            <DashboardCVHistoryView />
          </div>
        </TabsContent>
      </Tabs>
    </>
  );
}
