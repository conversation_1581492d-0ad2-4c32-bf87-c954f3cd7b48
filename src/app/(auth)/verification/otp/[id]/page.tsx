import { z } from "zod/v4";
import { OtpView } from "@/modules/auth/views/otp.view";
import { notFound } from "next/navigation";

const typeSchema = z.enum(["email", "phone"]);

export default async function VerificationOtpPage({
  params,
  searchParams,
}: {
  params: Promise<{ id: string }>;
  searchParams: Promise<{ type: "email" | "phone" }>;
}) {
  const { id } = await params;
  const { type } = await searchParams;

  if (!typeSchema.safeParse(type).success || !id) {
    notFound();
  }

  return (
    <div className="container mx-auto flex h-screen items-center justify-center">
      <OtpView email={id} />
    </div>
  );
}
