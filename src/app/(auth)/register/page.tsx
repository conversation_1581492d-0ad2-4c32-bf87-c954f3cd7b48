import { Metada<PERSON> } from "next";
import { cookies } from "next/headers";

import { RegisterView } from "@/modules/auth/views/register.view";

export const metadata: Metadata = {
  title: "Daftar",
};

export default async function RegisterPage() {
  const cookiesStore = await cookies()
  const isRegister = cookiesStore.get("register-token")?.value || "";
  
  
  return (
    <div className="container mx-auto flex h-screen items-center justify-center">
      <RegisterView isRegister={!!isRegister} />
    </div>
  );
}
