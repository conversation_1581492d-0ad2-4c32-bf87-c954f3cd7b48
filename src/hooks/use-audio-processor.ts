import { useRef, useState, useCallback, useEffect } from "react";

type UseAudioProcessorProps = {
  bufferSize?: number;
  sampleRate?: number;
  selectedDeviceId?: string;
  onMessage?: (data: Int16Array) => void;
  onError?: (error: Error) => void;
  onOpen?: () => void;
  onClose?: () => void;
  maxRetries?: number;
  retryDelay?: number;
};

type AudioDevice = {
  deviceId: string;
  label: string;
  groupId: string;
};

type WorkletMessage =
  | { debug: true; inputSampleRate: number; targetSampleRate: number }
  | Int16Array;

type RecordingState = "idle" | "connecting" | "connected" | "error";

export function encodeBase64(uint8: Uint8Array): string {
  let binary = "";
  const len = uint8.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(uint8[i]);
  }
  return btoa(binary);
}

export function bufferToChunk(buffer: ArrayBufferLike) {
  const uint8 = new Uint8Array(buffer);
  const chunk = encodeBase64(uint8);
  return chunk;
}

// Permission cache to avoid duplicate requests
let permissionGranted = false;
let cachedDevices: AudioDevice[] = [];

// LocalStorage key for persisting device selection
const AUDIO_DEVICE_STORAGE_KEY = "audio-processor-device-id";

export function useAudioProcessor({
  bufferSize = 4096 as const,
  sampleRate = 16000 as const,
  selectedDeviceId,
  onMessage,
  onError,
  onOpen,
  onClose,
  maxRetries = 3,
  retryDelay = 1000,
}: UseAudioProcessorProps) {
  const streamRef = useRef<MediaStream | null>(null);
  const audioRef = useRef<AudioContext | null>(null);
  const workletNodeRef = useRef<AudioWorkletNode | null>(null);
  const switchPromiseRef = useRef<Promise<void> | null>(null);
  const retryCountRef = useRef(0);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [recordingState, setRecordingState] = useState<RecordingState>("idle");
  const [audioDevices, setAudioDevices] = useState<AudioDevice[]>(cachedDevices);
  const [currentDeviceId, setCurrentDeviceId] = useState<string>(() => {
    // Try to get saved device ID from localStorage, fallback to selectedDeviceId or empty string
    if (typeof window !== "undefined") {
      const savedDeviceId = localStorage.getItem(AUDIO_DEVICE_STORAGE_KEY);
      return savedDeviceId || selectedDeviceId || "";
    }
    return selectedDeviceId || "";
  });

  const isRecording = recordingState === "connected";
  const isLoading = recordingState === "connecting";

  const cleanup = useCallback(() => {
    try {
      // Clear retry timeout if exists
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }

      // Disconnect and stop worklet node
      if (workletNodeRef.current) {
        workletNodeRef.current.port.onmessage = null;
        workletNodeRef.current.disconnect();
        workletNodeRef.current = null;
      }

      // Close audio context safely
      if (audioRef.current) {
        if (audioRef.current.state === "running") {
          audioRef.current.suspend();
        }
        if (audioRef.current.state !== "closed") {
          audioRef.current.close().catch(console.error);
        }
        audioRef.current = null;
      }

      // Stop all media tracks
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => {
          if (track.readyState === "live") {
            track.stop();
          }
        });
        streamRef.current = null;
      }
    } catch (err) {
      console.error("Error during cleanup:", err);
    }
  }, []);

  const getAudioDevices = useCallback(
    async (forceRefresh = false) => {
      try {
        // Use cached devices if permission already granted and not forcing refresh
        if (permissionGranted && cachedDevices.length > 0 && !forceRefresh) {
          setAudioDevices(cachedDevices);
          return cachedDevices;
        }

        // Request permission only if not already granted
        if (!permissionGranted) {
          const tempStream = await navigator.mediaDevices.getUserMedia({
            audio: {
              sampleRate,
              sampleSize: 16,
              channelCount: 1,
              echoCancellation: true,
              noiseSuppression: true,
              autoGainControl: true,
            },
          });
          tempStream.getTracks().forEach((track) => track.stop());
          permissionGranted = true;
        }

        const devices = await navigator.mediaDevices.enumerateDevices();
        const audioInputs = devices
          .filter((device) => device.kind === "audioinput")
          .map((device) => ({
            deviceId: device.deviceId,
            label: device.label || `Microphone ${device.deviceId.slice(0, 8)}`,
            groupId: device.groupId,
          }));

        cachedDevices = audioInputs;
        setAudioDevices(audioInputs);

        // Determine which device ID to use
        if (!currentDeviceId && !selectedDeviceId && audioInputs.length > 0) {
          // No current or selected device, use first available
          const firstDeviceId = audioInputs[0].deviceId;
          setCurrentDeviceId(firstDeviceId);
          if (typeof window !== "undefined") {
            localStorage.setItem(AUDIO_DEVICE_STORAGE_KEY, firstDeviceId);
          }
        } else if (selectedDeviceId && selectedDeviceId !== currentDeviceId) {
          // Use selected device if different from current
          setCurrentDeviceId(selectedDeviceId);
          if (typeof window !== "undefined") {
            localStorage.setItem(AUDIO_DEVICE_STORAGE_KEY, selectedDeviceId);
          }
        }

        return audioInputs;
      } catch (error) {
        console.error("Error getting audio devices:", error);
        onError?.(new Error("Failed to get audio devices"));
        return [];
      }
    },
    [sampleRate, selectedDeviceId, onError],
  );

  const connectWithRetry = useCallback(
    async (specificDeviceId?: string): Promise<void> => {
      try {
        setRecordingState("connecting");

        // Use specific device ID if provided, otherwise use current or selected
        const deviceId = specificDeviceId || currentDeviceId || selectedDeviceId;
        console.log("Connecting to device:", deviceId);

        // Clean up any existing stream before creating a new one
        if (streamRef.current) {
          streamRef.current.getTracks().forEach((track) => {
            if (track.readyState === "live") track.stop();
          });
          streamRef.current = null;
        }

        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate,
            channelCount: 1,
            deviceId: deviceId ? { exact: deviceId } : undefined,
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
          },
          video: false,
        });
        streamRef.current = stream;

        audioRef.current = new AudioContext({
          latencyHint: "interactive",
        });

        if (audioRef.current.state !== "running") {
          await audioRef.current.resume();
        }

        // * ScriptProcessorNode

        // const source = audioRef.current.createMediaStreamSource(stream);
        // const scriptProcessor = audioRef.current.createScriptProcessor(bufferSize, 1, 1);

        // scriptProcessor.onaudioprocess = (event: AudioProcessingEvent) => {
        //   const rawFloatData = event.inputBuffer.getChannelData(0);
        //   const pcm16Data = floatTo16BitPCM(rawFloatData);
        //   onMessage?.(pcm16Data);
        // };

        // source.connect(scriptProcessor);
        // scriptProcessor.connect(audioRef.current.destination);

        // * AudioWorkletNode

        const actualSampleRate = audioRef.current.sampleRate;

        await audioRef.current.audioWorklet.addModule("/audio-processor.js");
        const source = audioRef.current.createMediaStreamSource(stream);

        workletNodeRef.current = new AudioWorkletNode(audioRef.current, "audio-processor", {
          processorOptions: {
            bufferSize,
            inputSampleRate: actualSampleRate,
            targetSampleRate: sampleRate,
            echoCancellation: true,
            noiseSuppression: true,
          },
        });

        workletNodeRef.current.port.onmessage = (event: MessageEvent<WorkletMessage>) => {
          if ("debug" in event.data && event.data.debug) {
            console.log("Actual sample rate:", event.data.inputSampleRate);
            console.log("Target sample rate:", event.data.targetSampleRate);
            return;
          }
          const int16Data = event.data;
          onMessage?.(int16Data as Int16Array);
        };

        source.connect(workletNodeRef.current);
        workletNodeRef.current.connect(audioRef.current.destination);

        await onOpen?.();
        setRecordingState("connected");
        retryCountRef.current = 0; // Reset retry count on successful connection
      } catch (error) {
        console.error("Audio processing error:", error);

        // Implement retry logic
        if (retryCountRef.current < maxRetries) {
          retryCountRef.current++;
          console.log(`Retrying connection (${retryCountRef.current}/${maxRetries})...`);

          cleanup();
          setRecordingState("error");

          retryTimeoutRef.current = setTimeout(
            () => {
              connectWithRetry();
            },
            Math.min(retryDelay * retryCountRef.current, 5000),
          ); // Progressive delay with max 5 seconds
        } else {
          onError?.(error as Error);
          cleanup();
          setRecordingState("idle");
          retryCountRef.current = 0;
        }
      }
    },
    [
      currentDeviceId,
      selectedDeviceId,
      bufferSize,
      cleanup,
      onError,
      onMessage,
      onOpen,
      sampleRate,
      maxRetries,
      retryDelay,
    ],
  );

  const connect = useCallback(
    async (deviceId?: string) => {
      if (streamRef.current && recordingState === "connected" && !deviceId) return;

      // If a specific device is requested, update the current device and save to localStorage
      if (deviceId) {
        setCurrentDeviceId(deviceId);
        if (typeof window !== "undefined") {
          localStorage.setItem(AUDIO_DEVICE_STORAGE_KEY, deviceId);
        }
      }

      retryCountRef.current = 0; // Reset retry count
      await connectWithRetry(deviceId);
    },
    [recordingState, connectWithRetry],
  );

  const stop = useCallback(() => {
    if (recordingState === "idle") return;
    cleanup();
    setRecordingState("idle");
    retryCountRef.current = 0;
    onClose?.();
  }, [recordingState, cleanup, onClose]);

  const switchDevice = useCallback(
    async (deviceId: string): Promise<void> => {
      // If already switching, wait for it to complete
      if (switchPromiseRef.current) {
        await switchPromiseRef.current;
      }

      const switchPromise = (async () => {
        const wasRecording = recordingState === "connected";

        if (wasRecording) {
          // Stop current stream
          cleanup();
          setRecordingState("idle");
          onClose?.();

          // Wait for cleanup to complete
          await new Promise((resolve) => setTimeout(resolve, 100));
        }

        // Update device ID and save to localStorage
        setCurrentDeviceId(deviceId);
        if (typeof window !== "undefined") {
          localStorage.setItem(AUDIO_DEVICE_STORAGE_KEY, deviceId);
        }

        if (wasRecording) {
          // Reconnect with new device
          retryCountRef.current = 0;
          await connectWithRetry();
        }
      })();

      switchPromiseRef.current = switchPromise;

      try {
        await switchPromise;
      } finally {
        switchPromiseRef.current = null;
      }
    },
    [recordingState, cleanup, onClose, connectWithRetry],
  );

  const toggleAudio = useCallback(() => {
    if (recordingState === "connected") {
      stop();
    } else if (recordingState === "idle") {
      connect();
    }
  }, [recordingState, connect, stop]);

  useEffect(() => {
    if (selectedDeviceId && selectedDeviceId !== currentDeviceId) {
      // If recording, switch device; otherwise just update the ID
      if (recordingState === "connected") {
        switchDevice(selectedDeviceId);
      } else {
        setCurrentDeviceId(selectedDeviceId);
        if (typeof window !== "undefined") {
          localStorage.setItem(AUDIO_DEVICE_STORAGE_KEY, selectedDeviceId);
        }
      }
    }
  }, [selectedDeviceId, currentDeviceId, recordingState, switchDevice]);

  useEffect(() => {
    // Listen for device changes
    const handleDeviceChange = async () => {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioInputs = devices
        .filter((device) => device.kind === "audioinput")
        .map((device) => ({
          deviceId: device.deviceId,
          label: device.label || `Microphone ${device.deviceId.slice(0, 8)}`,
          groupId: device.groupId,
        }));

      cachedDevices = audioInputs;
      setAudioDevices(audioInputs);

      // Check if the saved device still exists
      if (currentDeviceId && !audioInputs.some((d) => d.deviceId === currentDeviceId)) {
        // Saved device no longer available, select first available
        if (audioInputs.length > 0) {
          const newDeviceId = audioInputs[0].deviceId;
          setCurrentDeviceId(newDeviceId);
          if (typeof window !== "undefined") {
            localStorage.setItem(AUDIO_DEVICE_STORAGE_KEY, newDeviceId);
          }
        }
      }
    };

    navigator.mediaDevices.addEventListener("devicechange", handleDeviceChange);

    return () => {
      navigator.mediaDevices.removeEventListener("devicechange", handleDeviceChange);
      cleanup();
    };
  }, [cleanup]);

  return {
    toggleAudio,
    connect,
    stop,
    switchDevice,
    getAudioDevices,
    isRecording,
    isLoading,
    audioDevices,
    currentDeviceId,
    recordingState,
  };
}
