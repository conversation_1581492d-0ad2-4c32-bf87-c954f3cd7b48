import { useSession } from "next-auth/react";
import { useCallback, useEffect, useRef, useState } from "react";

import { RouteResponse } from "@/types/api-route.type";
import { IDidResponse } from "@/types/response.type";

import { ConnectionStatus } from "./use-websocket";

const presenterInputByService = {
  talks: {
    source_url: "https://create-images-results.d-id.com/DefaultPresenters/Emma_f/v1_image.jpeg",
  },
  clips: {
    presenter_id: "v2_public_alex@qcvo4gupoy",
    driver_id: "e3nbserss8",
  },
};

type UseDidAvatarProps = {
  onOpen?: () => void;
  onClose?: () => void;
  onError?: (error: Event) => void;
  onMessage?: (data: Message) => void;
  onDataChannel?: (event: string) => void;
  reconnectAttempts?: number;
  reconnectInterval?: number;
};

type SendMessage =
  | {
      type: "init-stream";
      payload: (typeof presenterInputByService.clips | typeof presenterInputByService.talks) & {
        presenter_type: "clip" | "talk";
      };
    }
  | {
      type: "ice";
      payload: {
        session_id: string;
        candidate: string;
        sdpMid: string | null;
        sdpMLineIndex: number | null;
      };
    }
  | {
      type: "sdp";
      payload: {
        answer: RTCSessionDescriptionInit;
        session_id: string;
        presenter_type: "clip" | "talk";
      };
    }
  | {
      type: "stream-text";
      payload: {
        text: string;
      };
    };

type Message = {
  messageType: "init-stream" | "sdp" | "ice";
  id: string;
  offer: RTCSessionDescriptionInit;
  ice_servers: RTCIceServer[];
  session_id: string;
  candidate: RTCIceCandidateInit | null;
};

export function useDidAvatar(props: UseDidAvatarProps) {
  const {
    onOpen,
    onClose,
    onError,
    onMessage,
    onDataChannel,
    reconnectAttempts = 3,
    reconnectInterval = 3000,
  } = props;

  const ws = useRef<WebSocket | null>(null);
  const messageQueueRef = useRef<SendMessage[]>([]);

  const idleVideoRef = useRef<HTMLVideoElement | null>(null);
  const streamVideoRef = useRef<HTMLVideoElement | null>(null);
  const statsIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);
  const pcDataChannel = useRef<RTCDataChannel | null>(null);

  const reconnectTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const reconnectAttemptsRef = useRef(0);

  const [isLoading, setIsLoading] = useState(true);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>("disconnected");

  const [id, setId] = useState({ stream: "", session: "" });
  const [presenter, setPresenter] = useState<"clip" | "talk">("clip");
  const [lastMessage, setLastMessage] = useState<Message | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [eventLabel, setEventLabel] = useState("");
  const [status, setStatus] = useState({
    iceGatheringStatus: "",
    iceStatus: "",
    peerConnectionStatus: "",
    signalStatus: "",
    streamingStatus: "",
  });

  const { data: session } = useSession();

  const flushQueue = useCallback(() => {
    while (messageQueueRef.current.length && ws.current?.readyState === WebSocket.OPEN) {
      const msg = messageQueueRef.current.shift();
      if (msg) {
        ws.current.send(typeof msg === "string" ? msg : JSON.stringify(msg));
      }
    }
  }, []);

  const connect = useCallback(async () => {
    if (peerConnectionRef.current && peerConnectionRef.current.connectionState === "connected")
      return;

    setConnectionStatus("connecting");
    setIsLoading(true);
    cleanupWebSocket();

    try {
      const body = JSON.stringify({ type: "init", apiKey: session?.user.service_api_key });

      const res = await fetch("/api/stream/did", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body,
      });

      const response = (await res.json()) as RouteResponse<{
        did: IDidResponse;
        api: {
          key: string;
          url: string;
          websocketUrl: string;
          service: "clips" | "talks";
        };
      }>;

      if (!response.success) {
        throw new Error(response.message);
      }

      const { api } = response.data;
      const presenterType = api.service === "clips" ? "clip" : "talk";

      setPresenter(presenterType);

      const wsUrl = `${api.websocketUrl}?authorization=Basic ${encodeURIComponent(api.key)}`;

      ws.current = new WebSocket(wsUrl);

      ws.current.onopen = () => {
        reconnectAttemptsRef.current = 0;
        // setConnectionStatus("connected");
        flushQueue();

        const startStreamMessage: SendMessage = {
          type: "init-stream",
          payload: {
            ...presenterInputByService[api.service],
            presenter_type: presenterType,
          },
        };

        send(startStreamMessage);

        onOpen?.();
      };

      ws.current.onclose = () => {
        setConnectionStatus("disconnected");
        if (reconnectAttemptsRef.current < reconnectAttempts) {
          reconnectAttemptsRef.current++;
          reconnectTimeoutRef.current = setTimeout(() => connect(), reconnectInterval);
        }
        onClose?.();
      };

      // * Handle WebSocket Messages
      ws.current.onmessage = async (event) => {
        let data: Message;
        try {
          data = JSON.parse(event.data);
        } catch {
          data = event.data;
        }
        setLastMessage(data);
        setMessages((prev) => [...prev, data]);

        switch (data.messageType) {
          case "init-stream":
            const {
              id: newStreamId,
              offer,
              ice_servers: iceServers,
              session_id: newSessionId,
            } = data;

            setId({ stream: newStreamId, session: newSessionId });

            // * Implementation Peer Connection
            const sessionClientAnswer = await createPeerConnection(offer, iceServers);

            if (sessionClientAnswer) {
              const sdpMessage: SendMessage = {
                type: "sdp",
                payload: {
                  answer: sessionClientAnswer,
                  session_id: newSessionId,
                  presenter_type: presenterType,
                },
              };

              send(sdpMessage);
            }

            break;
          case "sdp":
            break;
          case "ice":
            if (data.candidate) {
              try {
                await peerConnectionRef.current?.addIceCandidate(
                  new RTCIceCandidate(data.candidate),
                );
              } catch (error) {
                console.error("Failed to add ICE candidate:", error);
              }
            }
            break;
          default:
            break;
        }

        onMessage?.(data);
      };

      ws.current.onerror = (error) => {
        setConnectionStatus("error");
        onError?.(error);
      };
    } catch (error) {
      console.error("WebSocket connection error:", error);
      setIsLoading(false);
      setConnectionStatus("error");
    }
  }, [
    flushQueue,
    onClose,
    onError,
    onMessage,
    onOpen,
    reconnectAttempts,
    reconnectInterval,
    session?.user.service_api_key,
  ]);

  // * Create Peer Connection
  const createPeerConnection = useCallback(
    async (offer: RTCSessionDescriptionInit, iceServers: RTCIceServer[]) => {
      if (!peerConnectionRef.current) {
        peerConnectionRef.current = new RTCPeerConnection({ iceServers });
        pcDataChannel.current = peerConnectionRef.current.createDataChannel("JanusDataChannel");
        pcDataChannel.current.addEventListener("message", onDataChannelMessage, true);

        peerConnectionRef.current.addEventListener(
          "icegatheringstatechange",
          onIceGatheringStateChange,
          true,
        );
        peerConnectionRef.current.addEventListener("icecandidate", onIceCandidate, true);
        peerConnectionRef.current.addEventListener(
          "iceconnectionstatechange",
          onIceConnectionStateChange,
          true,
        );
        peerConnectionRef.current.addEventListener(
          "connectionstatechange",
          onConnectionStateChange,
          true,
        );
        peerConnectionRef.current.addEventListener(
          "signalingstatechange",
          onSignalingStateChange,
          true,
        );

        peerConnectionRef.current.addEventListener("track", onTrack, true);
        peerConnectionRef.current.addTransceiver("video", { direction: "recvonly" });
        peerConnectionRef.current.addTransceiver("audio", { direction: "recvonly" });
      }

      await peerConnectionRef.current.setRemoteDescription(offer);
      const sessionClientAnswer = await peerConnectionRef.current.createAnswer();
      await peerConnectionRef.current.setLocalDescription(sessionClientAnswer);

      return sessionClientAnswer;
    },
    [],
  );

  const onTrack = useCallback((event: RTCTrackEvent) => {
    if (!event.track) return;

    // Clear existing interval before creating new one
    if (statsIntervalRef.current) {
      clearInterval(statsIntervalRef.current);
    }

    if (event.track.kind === "video" && event.streams[0] && streamVideoRef.current) {
      streamVideoRef.current.srcObject = event.streams[0];
      streamVideoRef.current.loop = false;
    }
  }, []);

  const onDataChannelMessage = useCallback(
    (message: MessageEvent) => {
      if (pcDataChannel.current && pcDataChannel.current.readyState === "open") {
        const [event] = message.data.split(":");
        console.log("EVENT LABEL", event);
        onDataChannel?.(event);
        setEventLabel(event);
      }
    },
    [onDataChannel],
  );

  const onIceGatheringStateChange = () => {
    setStatus((prev) => ({
      ...prev,
      iceGatheringStatus: peerConnectionRef.current?.iceGatheringState || "",
    }));
  };

  const onIceCandidate = (event: RTCPeerConnectionIceEvent) => {
    if (event.candidate) {
      const { candidate, sdpMid, sdpMLineIndex } = event.candidate;
      const iceMessage: SendMessage = {
        type: "ice",
        payload: { session_id: id.session, candidate, sdpMid, sdpMLineIndex },
      };
      send(iceMessage);
    }
  };

  const onIceConnectionStateChange = () => {
    setStatus((prev) => ({
      ...prev,
      iceStatus: peerConnectionRef.current?.iceConnectionState || "",
    }));

    console.log(
      `Peer ICE connection state changed to ${peerConnectionRef.current?.iceConnectionState}`,
    );
    if (peerConnectionRef.current?.iceConnectionState == "connected") {
      setConnectionStatus("connected");
      setIsLoading(false);
    }
  };

  const onConnectionStateChange = () => {
    setStatus((prev) => ({
      ...prev,
      peerConnectionStatus: peerConnectionRef.current?.connectionState || "",
    }));

    console.log(`Peer connection state changed to ${peerConnectionRef.current?.connectionState}`);
    if (peerConnectionRef.current?.connectionState == "connected") {
      setConnectionStatus("connected");
      setIsLoading(false);
    }
  };

  const onSignalingStateChange = () => {
    setStatus((prev) => ({
      ...prev,
      signalStatus: peerConnectionRef.current?.signalingState || "",
    }));
  };

  const disconnect = useCallback(() => {
    cleanupWebSocket();
    setConnectionStatus("disconnected");
  }, []);

  const cleanupWebSocket = useCallback(() => {
    // Clear any pending reconnect timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Clear stats interval
    if (statsIntervalRef.current) {
      clearInterval(statsIntervalRef.current);
      statsIntervalRef.current = null;
    }

    // Close peer connection
    if (peerConnectionRef.current) {
      peerConnectionRef.current.close();
      peerConnectionRef.current = null;
    }

    // Close data channel
    if (pcDataChannel.current) {
      pcDataChannel.current.close();
      pcDataChannel.current = null;
    }

    // Close WebSocket
    if (ws.current) {
      ws.current.onopen = null;
      ws.current.onmessage = null;
      ws.current.onclose = null;
      ws.current.onerror = null;
      ws.current.close();
      ws.current = null;
    }
  }, []);

  const sendStreamMessage = useCallback(
    (text: string) => {
      if (ws.current?.readyState === WebSocket.OPEN) {
        const chunks = text.split(" ");
        for (const chunk of chunks) {
          const streamMessage = {
            type: "stream-text",
            payload: {
              script: {
                type: "text",
                input: chunk + " ",
                provider: {
                  type: "microsoft",
                  voice_id: "en-US-JennyNeural",
                },
              },
              config: {
                stitch: true,
              },
              background: {
                color: "#FFFFFF",
              },
              session_id: id.session,
              stream_id: id.stream,
              presenter_type: presenter,
            },
          };

          if (
            peerConnectionRef.current?.connectionState === "connected" &&
            peerConnectionRef.current?.iceConnectionState === "connected"
          ) {
            ws.current.send(JSON.stringify(streamMessage));
          }
        }
      }
    },
    [id.session, id.stream, presenter],
  );

  const send = useCallback((message: SendMessage) => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      ws.current.send(typeof message === "string" ? message : JSON.stringify(message));

      return true;
    } else {
      messageQueueRef.current.push(message);
      return false;
    }
  }, []);

  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  useEffect(() => {
    const idleVideo = idleVideoRef.current;
    const streamVideo = streamVideoRef.current;

    if (!idleVideo || !streamVideo) return;

    if (eventLabel === "stream/started") {
      idleVideo.style.opacity = "0";
      streamVideo.style.opacity = "1";
      streamVideo.play().catch(console.error);
      setIsSpeaking(true);
    } else if (eventLabel === "stream/done") {
      idleVideo.style.opacity = "1";
      streamVideo.style.opacity = "0";
      setIsSpeaking(false);
      if (streamVideo.srcObject) {
        streamVideo.pause();
      }
    }
  }, [eventLabel]);

  return {
    id,
    idleVideoRef,
    streamVideoRef,
    connect,
    connectionStatus,
    disconnect,
    send,
    sendStreamMessage,
    eventLabel,
    lastMessage,
    messages,
    isLoading,
    status,
    isSpeaking,
  };
}
