import { useCallback, useEffect, useRef, useState } from "react";
import { useSession } from "next-auth/react";

import { ConnectionStatus } from "./use-websocket";
import { RouteResponse } from "@/types/api-route.type";
import { ISpeechMeticsResponse } from "@/types/response.type";

type UseSpeechmaticsProps = {
  onMessage?: (data: SpeechmaticsMessage) => void;
  onOpen?: () => void;
  onClose?: () => void;
  onError?: (error: Event) => void;
  onEndOfUtterance: () => void;
  reconnectAttempts?: number;
  reconnectInterval?: number;
  autoConnect?: boolean;
};

type SpeechmaticsMessage = {
  type: "transcript" | "end_of_utterance";
  transcript: string;
};

export function useSpeechmatics({
  onMessage,
  onOpen,
  onClose,
  onError,
  onEndOfUtterance,
  reconnectAttempts = 3,
  reconnectInterval = 3000,
}: UseSpeechmaticsProps) {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>("disconnected");
  const [lastMessage, setLastMessage] = useState<SpeechmaticsMessage | null>(null);
  const [messages, setMessages] = useState<SpeechmaticsMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const session = useSession();
  const ws = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const messageQueueRef = useRef<(SpeechmaticsMessage | ArrayBuffer)[]>([]);

  const connect = useCallback(async () => {
    if (ws.current?.readyState === WebSocket.OPEN) return;

    setConnectionStatus("connecting");
    setIsLoading(true);
    cleanupWebSocket();

    try {
      const res = await fetch("/api/stream/speechmatics", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ type: "init", apiKey: session?.data?.user.service_api_key }),
      });

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const response = (await res.json()) as RouteResponse<ISpeechMeticsResponse>;

      if (!response.success) {
        throw new Error(response.message || "Failed to initialize connection");
      }

      const url = response.data.url.replace("ws", "wss");

      const socket = new WebSocket(url);

      ws.current = socket;

      ws.current.onopen = () => {
        setConnectionStatus("connected");
        setIsLoading(false);
        reconnectAttemptsRef.current = 0;
        onOpen?.();
      };

      ws.current.onmessage = (event) => {
        try {
          const data: SpeechmaticsMessage = JSON.parse(event.data);
          if (data.type === "end_of_utterance") {
            onEndOfUtterance();
          }
          onMessage?.(data);
          if (data.type === "transcript" && data.transcript) {
            setMessages((prev) => [...prev, data]);
          }
        } catch (error) {
          console.error("Failed to parse message:", error);
        }
      };

      ws.current.onclose = () => {
        setConnectionStatus("disconnected");
        onClose?.();

        if (ws.current === socket && reconnectAttemptsRef.current < reconnectAttempts) {
          reconnectAttemptsRef.current++;
          reconnectTimeoutRef.current = setTimeout(() => connect(), reconnectInterval);
        }
      };

      ws.current.onerror = (error) => {
        setConnectionStatus("error");
        onError?.(error);
      };
    } catch (error) {
      setConnectionStatus("error");
      console.error("WebSocket connection error:", error);
    }
  }, [onClose, onError, onMessage, onOpen]);

  const cleanupWebSocket = useCallback(() => {
    if (ws.current) {
      ws.current.onopen = null;
      ws.current.onmessage = null;
      ws.current.onclose = null;
      ws.current.onerror = null;

      if (ws.current.readyState === WebSocket.OPEN) {
        try {
          ws.current.send(JSON.stringify({ type: "stop" }));
        } catch (error) {
          console.error("Error sending stop message:", error);
        }
      }

      ws.current.close();
      ws.current = null;
    }
  }, []);

  const doneTalk = useCallback(() => {
    let combinedTranscript = "";
    setMessages((prevMessages) => {
      combinedTranscript = prevMessages
        .map((m) => m.transcript)
        .join(" ")
        .trim();
      return [];
    });

    if (combinedTranscript) {
      setLastMessage({ type: "transcript", transcript: combinedTranscript });
    }
    return combinedTranscript;
  }, []);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    cleanupWebSocket();
    setConnectionStatus("disconnected");
  }, [cleanupWebSocket]);

  const send = useCallback(
    (message: SpeechmaticsMessage | string | ArrayBufferLike | Int16Array) => {
      if (ws.current?.readyState === WebSocket.OPEN) {
        ws.current.send(message as string);
        return true;
      } else {
        messageQueueRef.current.push(message as SpeechmaticsMessage | ArrayBuffer);
        return false;
      }
    },
    [],
  );

  const clearMessages = useCallback(() => {
    setMessages([]);
    setLastMessage(null);
  }, []);

  useEffect(() => {
    return () => {
      cleanupWebSocket();
    };
  }, [cleanupWebSocket]);

  return {
    isConnected: connectionStatus === "connected",
    isLoading,
    connectionStatus,
    lastMessage,
    messages,
    connect,
    disconnect,
    send,
    clearMessages,
    doneTalk,
  };
}
