import { useCallback, useState } from "react";

export interface Message {
  type: "ai" | "user";
  text: string;
  timestamp: Date;
}

export function useChatMessages() {
  const [messages, setMessages] = useState<Message[]>([
    // { type: "ai", text: "<PERSON><PERSON>, selamat datang di interview!", timestamp: new Date() },
    // { type: "user", text: "Siapakah namamu?", timestamp: new Date() },
    // { type: "ai", text: "Aku namanya G-Brain AI", timestamp: new Date() },
    // { type: "user", text: "Apa itu G-Brain AI?", timestamp: new Date() },
    // { type: "ai", text: "<PERSON>ku adalah AI yang akan menginterviewmu", timestamp: new Date() },
  ]);

  const addMessage = useCallback((message: Message) => {
    setMessages((prev) => [...prev, message]);
  }, []);

  const addStreamMessage = useCallback((text: string, type: "ai" | "user" = "ai") => {
    setMessages((prev) => {
      const lastMessage = prev[prev.length - 1];

      if (lastMessage?.type === type) {
        return [
          ...prev.slice(0, -1),
          { ...lastMessage, text: lastMessage.text + text },
        ];
      }

      return [...prev, { type, text, timestamp: new Date() }];
    });
  }, []);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  return {
    messages,
    addMessage,
    addStreamMessage,
    clearMessages,
  };
}
