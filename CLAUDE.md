# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AI Interview Platform built with Next.js 15 (React 19), featuring real-time video interviews with AI avatars, CV screening, and interview scheduling. Uses Supabase for storage, NextAuth for authentication, and WebSockets for real-time communication.

## Essential Commands

```bash
# Package Manager: pnpm@10.15.0 (required)
pnpm dev          # Start dev server with Turbopack on localhost:3000
pnpm build        # Build for production
pnpm start        # Start production server
pnpm lint         # Run ESLint (Next.js core-web-vitals + TypeScript)
pnpm shadcn:add   # Add shadcn/ui components
```

## Architecture & Patterns

### Module Structure
```
src/modules/{feature}/
├── components/   # UI components (prefer client components here)
├── views/        # Page-level components (server components)
├── services/     # Server actions ("use server")
└── schemas/      # Zod validation schemas
```

### Key Implementation Patterns

1. **Server Components First**: All page components and views are Server Components by default. Only add "use client" when necessary for interactivity.

2. **Server Actions**: All data mutations use Server Actions in `services/` files:
   - Must have "use server" directive
   - Return `ActionState<SchemaType, DataType>` type
   - Use `handleActionError` for error handling
   - Validate with Zod schemas from `schemas/` directory

3. **Authentication Flow**:
   - NextAuth v5 beta with JWT strategy
   - Auth config split between `auth.config.ts` and `src/auth.ts`
   - Google OAuth + Credentials providers
   - Session available via `auth()` in server components
   - Protected routes handled by middleware

4. **Real-time Communication**:
   - WebSocket hook: `use-websocket.ts` with auto-reconnect
   - LiveKit integration for video/audio (`@livekit/components-react`)
   - HeyGen streaming avatars (`@heygen/streaming-avatar`)
   - Audio processing: Gladia (`use-gladia.ts`), Speechmatics (`use-speechmatics.ts`)

5. **Styling System**:
   - Tailwind CSS v4 with PostCSS
   - CSS variables in `src/styles/variables.css` using OKLCH color space
   - Component variants with CVA (class-variance-authority)
   - Tailwind merge for className conflicts
   - Prettier plugin for Tailwind class sorting

6. **File Uploads**:
   - Supabase Storage integration
   - Upload hook: `use-file-upload.ts`
   - Image utilities: `src/utils/supabase/image.ts`
   - Max upload size: 10MB (configured in next.config.ts)

7. **Form Handling**:
   - Zod schemas for validation
   - Server Actions for submission
   - Field-level error handling with `flattenError` utility
   - Form state management with `ActionState` type

## Environment Variables

Required for development:
```
NEXT_PUBLIC_SITE_NAME
NEXT_PUBLIC_SITE_URL
NEXT_PUBLIC_API_URL           # Backend API endpoint
NEXT_PUBLIC_WS_API_URL         # WebSocket API endpoint
NEXT_PUBLIC_WS_HOST            # WebSocket host
NEXT_PUBLIC_SUPABASE_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY
AUTH_SECRET                    # NextAuth secret (generate with openssl rand -base64 32)
AUTH_GOOGLE_ID                 # Google OAuth client ID
AUTH_GOOGLE_SECRET             # Google OAuth client secret
```

## Code Conventions

- **TypeScript**: Strict mode enabled, use type imports when possible
- **Imports**: Use path aliases (@/* for src/*, @public/* for public/*)
- **Components**: PascalCase files, export default for pages
- **Server Actions**: async functions with proper error handling
- **Hooks**: Prefix with "use", return objects with consistent naming
- **Error Handling**: Use `tryCatch` utility for async operations
- **Date Formatting**: Use date-fns for all date operations

## Project-Specific Utilities

- `cn()`: Tailwind className merger
- `tryCatch()`: Async error handling wrapper
- `handleActionError()`: Server action error formatter
- `apiServer()`: Configured fetch wrapper for backend API
- `WEBSOCKET_CONFIG`: WebSocket configuration constants