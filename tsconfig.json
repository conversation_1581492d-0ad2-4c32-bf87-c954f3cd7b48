{
  "compilerOptions": {
    "target": "ES2017",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@public/*": [
        "./public/*"
      ]
    }
  },
  "include": [
    "next-env.d.ts",
    "svgr.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts", "src/hooks/use-did-avatar.vanilla.js",
  ],
  "exclude": [
    "node_modules"
  ]
}