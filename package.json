{"name": "next-ai-interview", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.15.0", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "shadcn:add": "pnpm dlx shadcn@latest add"}, "dependencies": {"@heygen/streaming-avatar": "^2.0.16", "@livekit/components-react": "^2.9.14", "@origin-space/image-cropper": "^0.1.9", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@remixicon/react": "^4.6.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.55.0", "@svgr/webpack": "^8.1.0", "@tanstack/react-query": "^5.85.5", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^6.1.0", "date-fns": "^4.1.0", "input-otp": "^1.4.2", "livekit-client": "^2.15.5", "lucide-react": "^0.518.0", "mixpanel": "^0.18.1", "motion": "^12.23.12", "nanoid": "^5.1.5", "next": "15.5.0", "next-auth": "5.0.0-beta.29", "next-themes": "^0.4.6", "qs": "^6.14.0", "react": "19.1.1", "react-day-picker": "^9.9.0", "react-dom": "19.1.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-phone-number-input": "^3.4.12", "react-use-measure": "^2.1.7", "recharts": "2.15.4", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.7", "tailwind-clamp": "^4.1.0", "tailwind-merge": "^3.3.1", "ws": "^8.18.3", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.12", "@types/node": "^20.19.11", "@types/qs": "^6.14.0", "@types/react": "19.1.10", "@types/react-dom": "19.1.7", "@types/ws": "^8.18.1", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-config-next": "15.5.0", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.12", "ts-node": "^10.9.2", "tsx": "^4.20.4", "tw-animate-css": "^1.3.7", "typescript": "^5.9.2"}, "pnpm": {"overrides": {"@types/react": "19.1.10", "@types/react-dom": "19.1.7"}, "onlyBuiltDependencies": ["@tailwindcss/oxide", "esbuild", "protobufjs", "sharp", "unrs-resolver"]}}